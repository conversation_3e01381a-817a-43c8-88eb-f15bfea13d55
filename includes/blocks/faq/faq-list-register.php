<?php
/**
 * Register the FAQ List block
 *
 * @package Marketing_Lawyers
 */

// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register the FAQ List block
 */
function ml_register_faq_list_block()
{
    // Skip block registration if <PERSON><PERSON><PERSON> is not enabled.
    if (!function_exists('register_block_type')) {
        return;
    }

    // Register the block script.
    wp_register_script(
        'ml-faq-list-block-editor',
        get_template_directory_uri() . '/includes/blocks/faq/faq-list.js',
        ['wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'],
        filemtime(get_template_directory() . '/includes/blocks/faq/faq-list.js'),
        true
    );

    // Register the block.
    register_block_type('ml/faq-list', [
        'editor_script' => 'ml-faq-list-block-editor',
        'render_callback' => 'ml_render_faq_list_block',
        'attributes' => [
            'title' => [
                'type' => 'string',
                'default' => '',
            ],
            'className' => [
                'type' => 'string',
                'default' => '',
            ],
        ],
    ]);
}
add_action('init', 'ml_register_faq_list_block');

/**
 * Render the FAQ List block
 *
 * @param array    $attributes The block attributes.
 * @param string   $content    The block content.
 * @return string The block HTML.
 */
function ml_render_faq_list_block($attributes, $content)
{
    $title = !empty($attributes['title']) ? $attributes['title'] : '';
    $class_name = !empty($attributes['className']) ? ' ' . $attributes['className'] : '';

    ob_start();
    ?>
    <div class="ml-faq-list<?php echo esc_attr($class_name); ?>">
        <?php if (!empty($title)) : ?>
            <h2 class="ml-faq-list-title border-none text-2xl font-bold mb-4 uppercase"><?php echo wp_kses_post($title); ?></h2>
        <?php endif; ?>
        <div class="ml-faq-items">
            <?php echo $content; ?>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
