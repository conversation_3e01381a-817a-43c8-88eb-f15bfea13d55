/**
 * FAQ Block Editor Styles
 */

.ml-faq-block-editor {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: #f8fafc;
    margin-bottom: 1rem;
}

.ml-faq-label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #4a5568;
}

.ml-faq-question-editor {
    margin-bottom: 1rem;
}

.ml-faq-question-input {
    padding: 0.75rem;
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    font-size: 1.125rem;
    font-weight: 500;
}

.ml-faq-answer-input {
    padding: 0.75rem;
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    min-height: 100px;
}

.ml-faq-question-input:focus,
.ml-faq-answer-input:focus {
    border-color: #4f46e5;
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}
