<?php
/**
 * Register the FAQ block
 *
 * @package Marketing_Lawyers
 */

// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register the FAQ block
 */
function ml_register_faq_block()
{
    // Skip block registration if <PERSON><PERSON><PERSON> is not enabled.
    if (!function_exists('register_block_type')) {
        return;
    }

    // Register the block script.
    wp_register_script(
        'ml-faq-block-editor',
        get_template_directory_uri() . '/includes/blocks/faq/block.js',
        ['wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'],
        filemtime(get_template_directory() . '/includes/blocks/faq/block.js'),
        true
    );

    // Register the block styles.
    wp_register_style(
        'ml-faq-block-editor-style',
        get_template_directory_uri() . '/includes/blocks/faq/editor.css',
        ['wp-edit-blocks'],
        filemtime(get_template_directory() . '/includes/blocks/faq/editor.css')
    );

    // Register the block.
    register_block_type('ml/faq', [
        'editor_script' => 'ml-faq-block-editor',
        'editor_style' => 'ml-faq-block-editor-style',
        'render_callback' => 'ml_render_faq_block',
        'attributes' => [
            'question' => [
                'type' => 'string',
                'default' => '',
            ],
            'answer' => [
                'type' => 'string',
                'default' => '',
            ],
            'initiallyOpen' => [
                'type' => 'boolean',
                'default' => false,
            ],
            'className' => [
                'type' => 'string',
                'default' => '',
            ],
        ],
    ]);
}
add_action('init', 'ml_register_faq_block');

/**
 * Render the FAQ block
 *
 * @param array $attributes The block attributes.
 * @return string The block HTML.
 */
function ml_render_faq_block($attributes)
{
    $question = !empty($attributes['question']) ? $attributes['question'] : '';
    $answer = !empty($attributes['answer']) ? $attributes['answer'] : '';
    $initially_open = !empty($attributes['initiallyOpen']) ? 'open' : '';
    $class_name = !empty($attributes['className']) ? ' ' . $attributes['className'] : '';

    // If no question or answer, return empty string.
    if (empty($question) && empty($answer)) {
        return '';
    }

    ob_start();
    ?>
    <details class="ml-faq-item mb-4 rounded-lg overflow-hidden bg-tertiary<?php echo esc_attr($class_name); ?>" <?php echo esc_attr($initially_open); ?>>
        <summary class="ml-faq-question flex justify-between items-center p-4 bg-tertiary cursor-pointer hover:bg-gray-100 transition-colors">
            <span class="font-bold"><?php echo wp_kses_post($question); ?></span>
            <?php echo get_svg_icon('chevron-down', 'ml-faq-icon text-secondary w-6 h-6 transform transition-transform'); ?>
        </summary>
        <div class="ml-faq-answer p-4 bg-white">
            <?php echo wp_kses_post(wpautop($answer)); ?>
        </div>
    </details>
    <?php
    return ob_get_clean();
}
