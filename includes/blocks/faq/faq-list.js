/**
 * FAQ List Block
 * 
 * A custom Gutenberg block for creating a list of FAQ items.
 */

(function(blocks, editor, components, i18n, element) {
    var el = element.createElement;
    var __ = i18n.__;
    var RichText = editor.RichText;
    var InnerBlocks = editor.InnerBlocks;

    // Register the block
    blocks.registerBlockType('ml/faq-list', {
        title: __('FAQ List', 'ml'),
        icon: 'list-view',
        category: 'common',
        keywords: [__('faq', 'ml'), __('questions', 'ml'), __('answers', 'ml')],
        attributes: {
            title: {
                type: 'string',
                default: '',
            },
        },

        // Define the edit interface
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;

            // Update title attribute
            function onChangeTitle(newTitle) {
                setAttributes({ title: newTitle });
            }

            return el('div', { className: props.className + ' ml-faq-list-editor' },
                el('div', { className: 'ml-faq-list-title-editor' },
                    el(RichText, {
                        tagName: 'h2',
                        className: 'ml-faq-list-title-input',
                        value: attributes.title,
                        onChange: onChangeTitle,
                        placeholder: __('FAQ Section Title (optional)', 'ml'),
                    })
                ),
                el('div', { className: 'ml-faq-list-items' },
                    el(InnerBlocks, {
                        allowedBlocks: ['ml/faq'],
                        template: [['ml/faq']],
                        templateLock: false,
                    })
                )
            );
        },

        // Define the save function (not used with dynamic blocks)
        save: function(props) {
            return el(InnerBlocks.Content);
        },
    });
})(
    window.wp.blocks,
    window.wp.blockEditor,
    window.wp.components,
    window.wp.i18n,
    window.wp.element
);
