/**
 * FAQ Block
 * 
 * A custom Gutenberg block for creating FAQ items with question and answer.
 */

(function(blocks, editor, components, i18n, element) {
    var el = element.createElement;
    var __ = i18n.__;
    var RichText = editor.RichText;
    var InspectorControls = editor.InspectorControls;
    var PanelBody = components.PanelBody;
    var ToggleControl = components.ToggleControl;

    // Register the block
    blocks.registerBlockType('ml/faq', {
        title: __('FAQ', 'ml'),
        icon: 'editor-help',
        category: 'common',
        keywords: [__('faq', 'ml'), __('question', 'ml'), __('answer', 'ml')],
        attributes: {
            question: {
                type: 'string',
                default: '',
            },
            answer: {
                type: 'string',
                default: '',
            },
            initiallyOpen: {
                type: 'boolean',
                default: false,
            },
        },

        // Define the edit interface
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;

            // Update question attribute
            function onChangeQuestion(newQuestion) {
                setAttributes({ question: newQuestion });
            }

            // Update answer attribute
            function onChangeAnswer(newAnswer) {
                setAttributes({ answer: newAnswer });
            }

            // Update initiallyOpen attribute
            function onChangeInitiallyOpen(newInitiallyOpen) {
                setAttributes({ initiallyOpen: newInitiallyOpen });
            }

            return [
                // Inspector controls for block settings
                el(InspectorControls, { key: 'inspector' },
                    el(PanelBody, {
                        title: __('FAQ Settings', 'ml'),
                        initialOpen: true,
                    },
                        el(ToggleControl, {
                            label: __('Initially Open', 'ml'),
                            checked: attributes.initiallyOpen,
                            onChange: onChangeInitiallyOpen,
                            help: attributes.initiallyOpen ? 
                                __('FAQ will be open by default.', 'ml') : 
                                __('FAQ will be closed by default.', 'ml'),
                        })
                    )
                ),
                
                // Block edit UI
                el('div', { className: props.className + ' ml-faq-block-editor' },
                    el('div', { className: 'ml-faq-question-editor' },
                        el('label', { className: 'ml-faq-label' }, __('Question', 'ml')),
                        el(RichText, {
                            tagName: 'h3',
                            className: 'ml-faq-question-input',
                            value: attributes.question,
                            onChange: onChangeQuestion,
                            placeholder: __('Enter question here...', 'ml'),
                        })
                    ),
                    el('div', { className: 'ml-faq-answer-editor' },
                        el('label', { className: 'ml-faq-label' }, __('Answer', 'ml')),
                        el(RichText, {
                            tagName: 'div',
                            className: 'ml-faq-answer-input',
                            value: attributes.answer,
                            onChange: onChangeAnswer,
                            placeholder: __('Enter answer here...', 'ml'),
                            multiline: 'p',
                        })
                    )
                )
            ];
        },

        // Define the save function (not used with dynamic blocks)
        save: function() {
            return null; // Dynamic block, rendering is handled by PHP
        },
    });
})(
    window.wp.blocks,
    window.wp.blockEditor,
    window.wp.components,
    window.wp.i18n,
    window.wp.element
);
