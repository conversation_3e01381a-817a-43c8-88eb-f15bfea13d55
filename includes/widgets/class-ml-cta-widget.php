<?php
/**
 * Call to Action Widget.
 *
 * Displays a call to action box in the sidebar
 */

// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Call to Action Widget Class.
 */
class ML_CTA_Widget extends WP_Widget
{
    /**
     * Constructor.
     */
    public function __construct()
    {
        parent::__construct(
            'ml_cta',
            __('Call to Action', 'ml'),
            [
                'description' => __('Displays a call to action box in the sidebar', 'ml'),
                'classname' => 'ml-cta-widget',
            ]
        );
    }

    /**
     * Front-end display of widget.
     *
     * @param array $args     Widget arguments.
     * @param array $instance Saved values from database.
     */
    public function widget($args, $instance)
    {
        echo $args['before_widget'];

        // Widget content
        ?>
        <div class="cta-box bg-tertiary p-6 rounded-lg border-l-4 border-primary">
            <?php if (!empty($instance['title'])) : ?>
                <h3 class="text-xl font-bold mb-3"><?php echo esc_html($instance['title']); ?></h3>
            <?php endif; ?>
            
            <?php if (!empty($instance['content'])) : ?>
                <div class="mb-4 text-gray-700">
                    <?php echo wpautop(wp_kses_post($instance['content'])); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($instance['button_text']) && !empty($instance['button_url'])) : ?>
                <a href="<?php echo esc_url($instance['button_url']); ?>" class="inline-block bg-primary hover:bg-opacity-90 text-white font-medium py-2 px-4 rounded transition-colors">
                    <?php echo esc_html($instance['button_text']); ?>
                </a>
            <?php endif; ?>
        </div>
        <?php

        echo $args['after_widget'];
    }

    /**
     * Back-end widget form.
     *
     * @param array $instance Previously saved values from database.
     */
    public function form($instance)
    {
        $title = !empty($instance['title']) ? $instance['title'] : __('Need Help?', 'ml');
        $content = !empty($instance['content']) ? $instance['content'] : __('Contact our team for a free consultation about your legal marketing needs.', 'ml');
        $button_text = !empty($instance['button_text']) ? $instance['button_text'] : __('Contact Us', 'ml');
        $button_url = !empty($instance['button_url']) ? $instance['button_url'] : '#';
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php esc_html_e('Title:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('content')); ?>"><?php esc_html_e('Content:', 'ml'); ?></label>
            <textarea class="widefat" id="<?php echo esc_attr($this->get_field_id('content')); ?>" name="<?php echo esc_attr($this->get_field_name('content')); ?>" rows="4"><?php echo esc_textarea($content); ?></textarea>
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('button_text')); ?>"><?php esc_html_e('Button Text:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('button_text')); ?>" name="<?php echo esc_attr($this->get_field_name('button_text')); ?>" type="text" value="<?php echo esc_attr($button_text); ?>">
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('button_url')); ?>"><?php esc_html_e('Button URL:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('button_url')); ?>" name="<?php echo esc_attr($this->get_field_name('button_url')); ?>" type="url" value="<?php echo esc_url($button_url); ?>">
        </p>
        <?php
    }

    /**
     * Sanitize widget form values as they are saved.
     *
     * @param array $new_instance Values just sent to be saved.
     * @param array $old_instance Previously saved values from database.
     *
     * @return array Updated safe values to be saved.
     */
    public function update($new_instance, $old_instance)
    {
        $instance = [];
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['content'] = (!empty($new_instance['content'])) ? wp_kses_post($new_instance['content']) : '';
        $instance['button_text'] = (!empty($new_instance['button_text'])) ? sanitize_text_field($new_instance['button_text']) : '';
        $instance['button_url'] = (!empty($new_instance['button_url'])) ? esc_url_raw($new_instance['button_url']) : '';

        return $instance;
    }
}
