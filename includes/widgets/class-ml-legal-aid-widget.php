<?php
/**
 * Legal Aid Widget.
 *
 * Displays a pre-styled Legal Aid section with a title and button
 */

// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Legal Aid Widget Class.
 */
class ML_Legal_Aid_Widget extends WP_Widget
{
    /**
     * Constructor.
     */
    public function __construct()
    {
        parent::__construct(
            'ml_legal_aid',
            __('Legal Aid Button', 'ml'),
            [
                'description' => __('Displays a pre-styled Legal Aid section with a title and button', 'ml'),
                'classname' => 'ml-legal-aid-widget',
            ]
        );
    }

    /**
     * Front-end display of widget.
     *
     * @param array $args     Widget arguments.
     * @param array $instance Saved values from database.
     */
    public function widget($args, $instance)
    {
        echo $args['before_widget'];

        // Get widget settings or use defaults
        $title = !empty($instance['title']) ? $instance['title'] : __('Legal Aid', 'ml');
        $button_text = !empty($instance['button_text']) ? $instance['button_text'] : __('Check Eligibility', 'ml');
        $button_url = !empty($instance['button_url']) ? $instance['button_url'] : '#';

        // Output the widget content
        ?>
        <div class="legal-aid-section">
            <h3 class="uppercase text-2xl font-bold mb-4"><?php echo esc_html($title); ?></h3>
            <a href="<?php echo esc_url($button_url); ?>" class="flex flex-row items-center justify-center gap-2 bg-secondary hover:bg-opacity-90 text-white font-medium py-2 px-4 rounded transition-colors">
                <?php echo esc_html($button_text); ?>
                <?php echo get_svg_icon('arrow-right', 'h-4 w-4 fill-white'); ?>
            </a>
        </div>
        <?php

        echo $args['after_widget'];
    }

    /**
     * Back-end widget form.
     *
     * @param array $instance Previously saved values from database.
     */
    public function form($instance)
    {
        $title = !empty($instance['title']) ? $instance['title'] : __('Legal Aid', 'ml');
        $button_text = !empty($instance['button_text']) ? $instance['button_text'] : __('Check Eligibility', 'ml');
        $button_url = !empty($instance['button_url']) ? $instance['button_url'] : '';
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php esc_html_e('Title:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('button_text')); ?>"><?php esc_html_e('Button Text:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('button_text')); ?>" name="<?php echo esc_attr($this->get_field_name('button_text')); ?>" type="text" value="<?php echo esc_attr($button_text); ?>">
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('button_url')); ?>"><?php esc_html_e('Button URL:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('button_url')); ?>" name="<?php echo esc_attr($this->get_field_name('button_url')); ?>" type="url" value="<?php echo esc_url($button_url); ?>">
        </p>


        <?php
    }

    /**
     * Sanitize widget form values as they are saved.
     *
     * @param array $new_instance Values just sent to be saved.
     * @param array $old_instance Previously saved values from database.
     *
     * @return array Updated safe values to be saved.
     */
    public function update($new_instance, $old_instance)
    {
        $instance = [];
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['button_text'] = (!empty($new_instance['button_text'])) ? sanitize_text_field($new_instance['button_text']) : '';
        $instance['button_url'] = (!empty($new_instance['button_url'])) ? esc_url_raw($new_instance['button_url']) : '';

        return $instance;
    }
}
