<?php
/**
 * Legal Links Widget.
 *
 * Displays legal links in the footer
 */

// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Legal Links Widget Class.
 */
class ML_Legal_Links_Widget extends WP_Widget
{
    /**
     * Constructor.
     */
    public function __construct()
    {
        parent::__construct(
            'ml_legal_links',
            __('Legal Links', 'ml'),
            [
                'description' => __('Displays legal links like Privacy Policy, Terms of Service, etc.', 'ml'),
                'classname' => 'ml-legal-links-widget',
            ]
        );
    }

    /**
     * Front-end display of widget.
     *
     * @param array $args     Widget arguments.
     * @param array $instance Saved values from database.
     */
    public function widget($args, $instance)
    {
        echo $args['before_widget'];

        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        $links = [];

        // Privacy Policy
        if (!empty($instance['privacy_policy_page']) && $instance['privacy_policy_page'] > 0) {
            $privacy_url = get_permalink($instance['privacy_policy_page']);
            $privacy_label = !empty($instance['privacy_policy_label']) ? $instance['privacy_policy_label'] : __('Privacy Policy', 'ml');
            $links[] = [
                'url' => $privacy_url,
                'label' => $privacy_label,
            ];
        }

        // Terms of Service
        if (!empty($instance['terms_page']) && $instance['terms_page'] > 0) {
            $terms_url = get_permalink($instance['terms_page']);
            $terms_label = !empty($instance['terms_label']) ? $instance['terms_label'] : __('Terms of Service', 'ml');
            $links[] = [
                'url' => $terms_url,
                'label' => $terms_label,
            ];
        }

        // Cookie Policy
        if (!empty($instance['cookie_page']) && $instance['cookie_page'] > 0) {
            $cookie_url = get_permalink($instance['cookie_page']);
            $cookie_label = !empty($instance['cookie_label']) ? $instance['cookie_label'] : __('Cookie Policy', 'ml');
            $links[] = [
                'url' => $cookie_url,
                'label' => $cookie_label,
            ];
        }

        // Custom Link 1
        if (!empty($instance['custom_url_1']) && !empty($instance['custom_label_1'])) {
            $links[] = [
                'url' => $instance['custom_url_1'],
                'label' => $instance['custom_label_1'],
            ];
        }

        // Custom Link 2
        if (!empty($instance['custom_url_2']) && !empty($instance['custom_label_2'])) {
            $links[] = [
                'url' => $instance['custom_url_2'],
                'label' => $instance['custom_label_2'],
            ];
        }

        if (!empty($links)) {
            echo '<div class="legal-links">';
            foreach ($links as $index => $link) {
                echo '<a href="' . esc_url($link['url']) . '" class="text-gray-400 hover:text-gray-300 transition-colors">' . esc_html($link['label']) . '</a>';
                if ($index < count($links) - 1) {
                    echo '<span class="mx-2 text-gray-500">|</span>';
                }
            }
            echo '</div>';
        }

        echo $args['after_widget'];
    }

    /**
     * Back-end widget form.
     *
     * @param array $instance Previously saved values from database.
     */
    public function form($instance)
    {
        $title = !empty($instance['title']) ? $instance['title'] : '';
        $privacy_policy_page = !empty($instance['privacy_policy_page']) ? $instance['privacy_policy_page'] : 0;
        $privacy_policy_label = !empty($instance['privacy_policy_label']) ? $instance['privacy_policy_label'] : __('Privacy Policy', 'ml');
        $terms_page = !empty($instance['terms_page']) ? $instance['terms_page'] : 0;
        $terms_label = !empty($instance['terms_label']) ? $instance['terms_label'] : __('Terms of Service', 'ml');
        $cookie_page = !empty($instance['cookie_page']) ? $instance['cookie_page'] : 0;
        $cookie_label = !empty($instance['cookie_label']) ? $instance['cookie_label'] : __('Cookie Policy', 'ml');
        $custom_url_1 = !empty($instance['custom_url_1']) ? $instance['custom_url_1'] : '';
        $custom_label_1 = !empty($instance['custom_label_1']) ? $instance['custom_label_1'] : '';
        $custom_url_2 = !empty($instance['custom_url_2']) ? $instance['custom_url_2'] : '';
        $custom_label_2 = !empty($instance['custom_label_2']) ? $instance['custom_label_2'] : '';

        // Get all pages for dropdown
        $pages = get_pages();
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php esc_html_e('Title (not displayed):', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" value="<?php echo esc_attr($title); ?>">
            <small><?php esc_html_e('This title is for admin purposes only and will not be displayed on the front end.', 'ml'); ?></small>
        </p>

        <p>
            <label for="<?php echo esc_attr($this->get_field_id('privacy_policy_page')); ?>"><?php esc_html_e('Privacy Policy Page:', 'ml'); ?></label>
            <select class="widefat" id="<?php echo esc_attr($this->get_field_id('privacy_policy_page')); ?>" name="<?php echo esc_attr($this->get_field_name('privacy_policy_page')); ?>">
                <option value="0"><?php esc_html_e('-- Select Page --', 'ml'); ?></option>
                <?php foreach ($pages as $page) : ?>
                    <option value="<?php echo esc_attr($page->ID); ?>" <?php selected($privacy_policy_page, $page->ID); ?>><?php echo esc_html($page->post_title); ?></option>
                <?php endforeach; ?>
            </select>
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('privacy_policy_label')); ?>"><?php esc_html_e('Privacy Policy Label:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('privacy_policy_label')); ?>" name="<?php echo esc_attr($this->get_field_name('privacy_policy_label')); ?>" type="text" value="<?php echo esc_attr($privacy_policy_label); ?>">
        </p>

        <p>
            <label for="<?php echo esc_attr($this->get_field_id('terms_page')); ?>"><?php esc_html_e('Terms of Service Page:', 'ml'); ?></label>
            <select class="widefat" id="<?php echo esc_attr($this->get_field_id('terms_page')); ?>" name="<?php echo esc_attr($this->get_field_name('terms_page')); ?>">
                <option value="0"><?php esc_html_e('-- Select Page --', 'ml'); ?></option>
                <?php foreach ($pages as $page) : ?>
                    <option value="<?php echo esc_attr($page->ID); ?>" <?php selected($terms_page, $page->ID); ?>><?php echo esc_html($page->post_title); ?></option>
                <?php endforeach; ?>
            </select>
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('terms_label')); ?>"><?php esc_html_e('Terms of Service Label:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('terms_label')); ?>" name="<?php echo esc_attr($this->get_field_name('terms_label')); ?>" type="text" value="<?php echo esc_attr($terms_label); ?>">
        </p>

        <p>
            <label for="<?php echo esc_attr($this->get_field_id('cookie_page')); ?>"><?php esc_html_e('Cookie Policy Page:', 'ml'); ?></label>
            <select class="widefat" id="<?php echo esc_attr($this->get_field_id('cookie_page')); ?>" name="<?php echo esc_attr($this->get_field_name('cookie_page')); ?>">
                <option value="0"><?php esc_html_e('-- Select Page --', 'ml'); ?></option>
                <?php foreach ($pages as $page) : ?>
                    <option value="<?php echo esc_attr($page->ID); ?>" <?php selected($cookie_page, $page->ID); ?>><?php echo esc_html($page->post_title); ?></option>
                <?php endforeach; ?>
            </select>
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('cookie_label')); ?>"><?php esc_html_e('Cookie Policy Label:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('cookie_label')); ?>" name="<?php echo esc_attr($this->get_field_name('cookie_label')); ?>" type="text" value="<?php echo esc_attr($cookie_label); ?>">
        </p>

        <p>
            <label for="<?php echo esc_attr($this->get_field_id('custom_url_1')); ?>"><?php esc_html_e('Custom Link 1 URL:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('custom_url_1')); ?>" name="<?php echo esc_attr($this->get_field_name('custom_url_1')); ?>" type="url" value="<?php echo esc_url($custom_url_1); ?>">
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('custom_label_1')); ?>"><?php esc_html_e('Custom Link 1 Label:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('custom_label_1')); ?>" name="<?php echo esc_attr($this->get_field_name('custom_label_1')); ?>" type="text" value="<?php echo esc_attr($custom_label_1); ?>">
        </p>

        <p>
            <label for="<?php echo esc_attr($this->get_field_id('custom_url_2')); ?>"><?php esc_html_e('Custom Link 2 URL:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('custom_url_2')); ?>" name="<?php echo esc_attr($this->get_field_name('custom_url_2')); ?>" type="url" value="<?php echo esc_url($custom_url_2); ?>">
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('custom_label_2')); ?>"><?php esc_html_e('Custom Link 2 Label:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('custom_label_2')); ?>" name="<?php echo esc_attr($this->get_field_name('custom_label_2')); ?>" type="text" value="<?php echo esc_attr($custom_label_2); ?>">
        </p>
        <?php
    }

    /**
     * Sanitize widget form values as they are saved.
     *
     * @param array $new_instance Values just sent to be saved.
     * @param array $old_instance Previously saved values from database.
     *
     * @return array Updated safe values to be saved.
     */
    public function update($new_instance, $old_instance)
    {
        $instance = [];
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['privacy_policy_page'] = (!empty($new_instance['privacy_policy_page'])) ? absint($new_instance['privacy_policy_page']) : 0;
        $instance['privacy_policy_label'] = (!empty($new_instance['privacy_policy_label'])) ? sanitize_text_field($new_instance['privacy_policy_label']) : '';
        $instance['terms_page'] = (!empty($new_instance['terms_page'])) ? absint($new_instance['terms_page']) : 0;
        $instance['terms_label'] = (!empty($new_instance['terms_label'])) ? sanitize_text_field($new_instance['terms_label']) : '';
        $instance['cookie_page'] = (!empty($new_instance['cookie_page'])) ? absint($new_instance['cookie_page']) : 0;
        $instance['cookie_label'] = (!empty($new_instance['cookie_label'])) ? sanitize_text_field($new_instance['cookie_label']) : '';
        $instance['custom_url_1'] = (!empty($new_instance['custom_url_1'])) ? esc_url_raw($new_instance['custom_url_1']) : '';
        $instance['custom_label_1'] = (!empty($new_instance['custom_label_1'])) ? sanitize_text_field($new_instance['custom_label_1']) : '';
        $instance['custom_url_2'] = (!empty($new_instance['custom_url_2'])) ? esc_url_raw($new_instance['custom_url_2']) : '';
        $instance['custom_label_2'] = (!empty($new_instance['custom_label_2'])) ? sanitize_text_field($new_instance['custom_label_2']) : '';

        return $instance;
    }
}
