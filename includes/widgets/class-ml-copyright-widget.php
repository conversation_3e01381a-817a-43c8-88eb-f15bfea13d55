<?php
/**
 * Copyright Widget.
 *
 * Displays a copyright notice in the footer
 */

// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Copyright Widget Class.
 */
class ML_Copyright_Widget extends WP_Widget
{
    /**
     * Constructor.
     */
    public function __construct()
    {
        parent::__construct(
            'ml_copyright',
            __('Copyright Notice', 'ml'),
            [
                'description' => __('Displays a copyright notice with the current year and site name', 'ml'),
                'classname' => 'ml-copyright-widget',
            ]
        );
    }

    /**
     * Front-end display of widget.
     *
     * @param array $args     Widget arguments.
     * @param array $instance Saved values from database.
     */
    public function widget($args, $instance)
    {
        echo $args['before_widget'];

        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        $prefix = !empty($instance['prefix']) ? $instance['prefix'] . ' ' : '&copy; ';
        $year = date('Y');
        $start_year = !empty($instance['start_year']) ? $instance['start_year'] : $year;
        $year_display = ($start_year != $year && !empty($instance['show_start_year'])) ? $start_year . '-' . $year : $year;
        $site_name = !empty($instance['custom_name']) ? $instance['custom_name'] : get_bloginfo('name');
        $suffix = !empty($instance['suffix']) ? ' ' . $instance['suffix'] : '. All rights reserved.';

        echo '<span class="copyright-text  text-sm text-left">' . esc_html($prefix) . esc_html($year_display) . ' ' . esc_html($site_name) . esc_html($suffix) . '</span>';

        echo $args['after_widget'];
    }

    /**
     * Back-end widget form.
     *
     * @param array $instance Previously saved values from database.
     */
    public function form($instance)
    {
        $title = !empty($instance['title']) ? $instance['title'] : '';
        $prefix = !empty($instance['prefix']) ? $instance['prefix'] : '&copy;';
        $start_year = !empty($instance['start_year']) ? $instance['start_year'] : '';
        $show_start_year = !empty($instance['show_start_year']) ? (bool) $instance['show_start_year'] : false;
        $custom_name = !empty($instance['custom_name']) ? $instance['custom_name'] : '';
        $suffix = !empty($instance['suffix']) ? $instance['suffix'] : 'All rights reserved.';
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php esc_html_e('Title (not displayed):', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" value="<?php echo esc_attr($title); ?>">
            <small><?php esc_html_e('This title is for admin purposes only and will not be displayed on the front end.', 'ml'); ?></small>
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('prefix')); ?>"><?php esc_html_e('Prefix:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('prefix')); ?>" name="<?php echo esc_attr($this->get_field_name('prefix')); ?>" type="text" value="<?php echo esc_attr($prefix); ?>">
            <small><?php esc_html_e('Text to display before the year. Default: &copy;', 'ml'); ?></small>
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('start_year')); ?>"><?php esc_html_e('Start Year:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('start_year')); ?>" name="<?php echo esc_attr($this->get_field_name('start_year')); ?>" type="number" min="1900" max="<?php echo date('Y'); ?>" value="<?php echo esc_attr($start_year); ?>">
            <small><?php esc_html_e('If you want to show a year range (e.g., 2020-2023), enter the start year here.', 'ml'); ?></small>
        </p>
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_start_year); ?> id="<?php echo esc_attr($this->get_field_id('show_start_year')); ?>" name="<?php echo esc_attr($this->get_field_name('show_start_year')); ?>">
            <label for="<?php echo esc_attr($this->get_field_id('show_start_year')); ?>"><?php esc_html_e('Show year range', 'ml'); ?></label>
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('custom_name')); ?>"><?php esc_html_e('Custom Name:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('custom_name')); ?>" name="<?php echo esc_attr($this->get_field_name('custom_name')); ?>" type="text" value="<?php echo esc_attr($custom_name); ?>">
            <small><?php esc_html_e('Leave blank to use site name.', 'ml'); ?></small>
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('suffix')); ?>"><?php esc_html_e('Suffix:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('suffix')); ?>" name="<?php echo esc_attr($this->get_field_name('suffix')); ?>" type="text" value="<?php echo esc_attr($suffix); ?>">
            <small><?php esc_html_e('Text to display after the site name. Default: All rights reserved.', 'ml'); ?></small>
        </p>
        <?php
    }

    /**
     * Sanitize widget form values as they are saved.
     *
     * @param array $new_instance Values just sent to be saved.
     * @param array $old_instance Previously saved values from database.
     *
     * @return array Updated safe values to be saved.
     */
    public function update($new_instance, $old_instance)
    {
        $instance = [];
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['prefix'] = (!empty($new_instance['prefix'])) ? sanitize_text_field($new_instance['prefix']) : '';
        $instance['start_year'] = (!empty($new_instance['start_year'])) ? absint($new_instance['start_year']) : '';
        $instance['show_start_year'] = (!empty($new_instance['show_start_year'])) ? 1 : 0;
        $instance['custom_name'] = (!empty($new_instance['custom_name'])) ? sanitize_text_field($new_instance['custom_name']) : '';
        $instance['suffix'] = (!empty($new_instance['suffix'])) ? sanitize_text_field($new_instance['suffix']) : '';

        return $instance;
    }
}
