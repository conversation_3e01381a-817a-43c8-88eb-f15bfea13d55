<?php
/**
 * Office Selector Widget.
 *
 * Displays a selected office's name and address in the footer
 */

// Exit if accessed directly.
if (! defined('ABSPATH')) {
    exit;
}

/**
 * Office Selector Widget Class.
 */
class ML_Office_Selector_Widget extends WP_Widget
{
    /**
     * Constructor.
     */
    public function __construct()
    {
        parent::__construct(
            'ml_office_selector',
            __('Office Display', 'ml'),
            [
                'description' => __('Displays a selected office\'s name and address', 'ml'),
                'classname' => 'ml-office-display-widget',
            ]
        );
    }

    /**
     * Front-end display of widget.
     *
     * @param  array  $args  Widget arguments.
     * @param  array  $instance  Saved values from database.
     */
    public function widget($args, $instance)
    {
        echo $args['before_widget'];

        if (! empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        // Get the selected office ID from widget settings
        $selected_office_id = ! empty($instance['selected_office']) ? $instance['selected_office'] : 0;

        if (! $selected_office_id) {
            echo '<p>' . __('No office selected. Please configure this widget.', 'ml') . '</p>';
            echo $args['after_widget'];

            return;
        }

        // Get office details
        $office_name = get_field('office_name', $selected_office_id) ? get_field('office_name', $selected_office_id) : get_the_title($selected_office_id);
        $office_location = get_field('office_location', $selected_office_id);

        ?>
        <div class="ml-office-display">
            <div class="office-details">
                <?php if (! empty($instance['show_link']) && $instance['show_link']) : ?>
                    <a href="<?php echo esc_url(get_permalink($selected_office_id)); ?>" class=" hover:text-primary transition-colors">
                    <h4 class="text-lg font-bold mb-2"><?php echo esc_html(! empty($instance['link_text']) ? $instance['link_text'] : __('View Office Details', 'ml')); ?></h4>
                    </a>
                <?php else: ?>
                    <h4 class="text-lg font-bold mb-2"><?php echo esc_html($office_name); ?></h4>
                <?php endif; ?>

                <?php if (! empty($office_location) && ! empty($office_location['address'])) {
                    // Extract postal code from address
                    $address = $office_location['address'];
                    $postal_code = '';

                    // UK postal code regex pattern
                    $uk_postcode_pattern = '/([A-Z]{1,2}[0-9][A-Z0-9]? ?[0-9][A-Z]{2}|[A-Z]{1,2}[0-9][A-Z0-9]?)/i';

                    // Check if we can find a UK postal code
                    if (preg_match($uk_postcode_pattern, $address, $matches)) {
                        $postal_code = $matches[0];
                        // Remove the postal code from the address for display
                        $address_without_postcode = preg_replace($uk_postcode_pattern, '', $address);
                        // Clean up any trailing commas or whitespace
                        $address_without_postcode = rtrim($address_without_postcode, ", \t\n\r\0\x0B");
                    } else {
                        $address_without_postcode = $address;
                    }
                    ?>
                    <address class="not-italic mb-4">
                        <?php echo nl2br(esc_html(str_replace(',', ",\n", $address_without_postcode))); ?>
                        <?php if (!empty($postal_code)) : ?>
                            <div class="font-bold mt-1"><?php echo esc_html($postal_code); ?></div>
                        <?php endif; ?>
                    </address>
                <?php } ?>
            </div>


        </div>
        <?php

        echo $args['after_widget'];
    }

    /**
     * Back-end widget form.
     *
     * @param  array  $instance  Previously saved values from database.
     */
    public function form($instance)
    {
        $title = ! empty($instance['title']) ? $instance['title'] : __('Our Office', 'ml');
        $selected_office = ! empty($instance['selected_office']) ? $instance['selected_office'] : 0;
        $show_link = isset($instance['show_link']) ? (bool) $instance['show_link'] : true;
        $link_text = ! empty($instance['link_text']) ? $instance['link_text'] : __('View Office Details', 'ml');

        // Get all offices
        $offices = get_posts([
            'post_type' => 'office',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC',
        ]);
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php esc_html_e('Title:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>

        <p>
            <label for="<?php echo esc_attr($this->get_field_id('selected_office')); ?>"><?php esc_html_e('Select Office:', 'ml'); ?></label>
            <select class="widefat" id="<?php echo esc_attr($this->get_field_id('selected_office')); ?>" name="<?php echo esc_attr($this->get_field_name('selected_office')); ?>">
                <option value="0"><?php esc_html_e('— Select —', 'ml'); ?></option>
                <?php foreach ($offices as $office) {
                    $office_display_name = get_field('office_name', $office->ID) ? get_field('office_name', $office->ID) : $office->post_title;
                    ?>
                    <option value="<?php echo esc_attr($office->ID); ?>" <?php selected($selected_office, $office->ID); ?>>
                        <?php echo esc_html($office_display_name); ?>
                    </option>
                <?php } ?>
            </select>
        </p>

        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_link); ?> id="<?php echo esc_attr($this->get_field_id('show_link')); ?>" name="<?php echo esc_attr($this->get_field_name('show_link')); ?>" />
            <label for="<?php echo esc_attr($this->get_field_id('show_link')); ?>"><?php esc_html_e('Show link to office page', 'ml'); ?></label>
        </p>

        <p>
            <label for="<?php echo esc_attr($this->get_field_id('link_text')); ?>"><?php esc_html_e('Link Text:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('link_text')); ?>" name="<?php echo esc_attr($this->get_field_name('link_text')); ?>" type="text" value="<?php echo esc_attr($link_text); ?>">
        </p>
        <?php
    }

    /**
     * Sanitize widget form values as they are saved.
     *
     * @param  array  $new_instance  Values just sent to be saved.
     * @param  array  $old_instance  Previously saved values from database.
     *
     * @return array Updated safe values to be saved.
     */
    public function update($new_instance, $old_instance)
    {
        $instance = [];
        $instance['title'] = ! empty($new_instance['title']) ? sanitize_text_field($new_instance['title']) : '';
        $instance['selected_office'] = ! empty($new_instance['selected_office']) ? intval($new_instance['selected_office']) : 0;
        $instance['show_link'] = isset($new_instance['show_link']) ? (bool) $new_instance['show_link'] : false;
        $instance['link_text'] = ! empty($new_instance['link_text']) ? sanitize_text_field($new_instance['link_text']) : '';

        return $instance;
    }
}
