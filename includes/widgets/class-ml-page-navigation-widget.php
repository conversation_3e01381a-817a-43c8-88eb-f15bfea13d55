<?php
/**
 * Page Navigation Widget.
 *
 * Displays navigation links to child pages of the current page
 */

// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Page Navigation Widget Class.
 */
class ML_Page_Navigation_Widget extends WP_Widget
{
    /**
     * Constructor.
     */
    public function __construct()
    {
        parent::__construct(
            'ml_page_navigation',
            __('Page Navigation', 'ml'),
            [
                'description' => __('Displays navigation links to child pages of the current page', 'ml'),
                'classname' => 'ml-page-navigation-widget',
            ]
        );
    }

    /**
     * Front-end display of widget.
     *
     * @param array $args     Widget arguments.
     * @param array $instance Saved values from database.
     */
    public function widget($args, $instance)
    {
        echo $args['before_widget'];

        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        // Get the current page ID
        $current_page_id = get_queried_object_id();
        
        // Check if we're on a page
        if (is_page()) {
            // Get parent page ID if this is a child page
            $parent_id = wp_get_post_parent_id($current_page_id);
            
            // If this is a child page, show siblings (pages with the same parent)
            if ($parent_id) {
                $parent_title = get_the_title($parent_id);
                $child_pages = get_pages([
                    'child_of' => $parent_id,
                    'sort_column' => 'menu_order,post_title',
                ]);
                
                if (!empty($child_pages)) {
                    echo '<div class="page-navigation">';
                    echo '<p class="parent-page mb-2"><a href="' . esc_url(get_permalink($parent_id)) . '" class="text-primary hover:underline font-medium">' . esc_html($parent_title) . '</a></p>';
                    echo '<ul class="page-list space-y-2">';
                    
                    foreach ($child_pages as $child) {
                        $is_current = ($child->ID == $current_page_id) ? ' font-bold text-primary' : '';
                        echo '<li class="page-item' . $is_current . '">';
                        echo '<a href="' . esc_url(get_permalink($child->ID)) . '" class="hover:text-primary transition-colors">' . esc_html($child->post_title) . '</a>';
                        echo '</li>';
                    }
                    
                    echo '</ul>';
                    echo '</div>';
                }
            } else {
                // This is a parent page, show its children
                $child_pages = get_pages([
                    'child_of' => $current_page_id,
                    'sort_column' => 'menu_order,post_title',
                ]);
                
                if (!empty($child_pages)) {
                    echo '<div class="page-navigation">';
                    echo '<ul class="page-list space-y-2">';
                    
                    foreach ($child_pages as $child) {
                        echo '<li class="page-item">';
                        echo '<a href="' . esc_url(get_permalink($child->ID)) . '" class="hover:text-primary transition-colors">' . esc_html($child->post_title) . '</a>';
                        echo '</li>';
                    }
                    
                    echo '</ul>';
                    echo '</div>';
                } else {
                    // No child pages found
                    if (!empty($instance['show_empty_message']) && $instance['show_empty_message']) {
                        echo '<p class="no-pages-message">' . esc_html($instance['empty_message']) . '</p>';
                    }
                }
            }
        } else {
            // Not on a page, show nothing or a message
            if (!empty($instance['show_empty_message']) && $instance['show_empty_message']) {
                echo '<p class="no-pages-message">' . esc_html($instance['empty_message']) . '</p>';
            }
        }

        echo $args['after_widget'];
    }

    /**
     * Back-end widget form.
     *
     * @param array $instance Previously saved values from database.
     */
    public function form($instance)
    {
        $title = !empty($instance['title']) ? $instance['title'] : __('Page Navigation', 'ml');
        $show_empty_message = isset($instance['show_empty_message']) ? (bool) $instance['show_empty_message'] : false;
        $empty_message = !empty($instance['empty_message']) ? $instance['empty_message'] : __('No subpages found.', 'ml');
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php esc_html_e('Title:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_empty_message); ?> id="<?php echo esc_attr($this->get_field_id('show_empty_message')); ?>" name="<?php echo esc_attr($this->get_field_name('show_empty_message')); ?>">
            <label for="<?php echo esc_attr($this->get_field_id('show_empty_message')); ?>"><?php esc_html_e('Show message when no pages found', 'ml'); ?></label>
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('empty_message')); ?>"><?php esc_html_e('Empty message:', 'ml'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('empty_message')); ?>" name="<?php echo esc_attr($this->get_field_name('empty_message')); ?>" type="text" value="<?php echo esc_attr($empty_message); ?>">
        </p>
        <?php
    }

    /**
     * Sanitize widget form values as they are saved.
     *
     * @param array $new_instance Values just sent to be saved.
     * @param array $old_instance Previously saved values from database.
     *
     * @return array Updated safe values to be saved.
     */
    public function update($new_instance, $old_instance)
    {
        $instance = [];
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['show_empty_message'] = (!empty($new_instance['show_empty_message'])) ? 1 : 0;
        $instance['empty_message'] = (!empty($new_instance['empty_message'])) ? sanitize_text_field($new_instance['empty_message']) : '';

        return $instance;
    }
}
