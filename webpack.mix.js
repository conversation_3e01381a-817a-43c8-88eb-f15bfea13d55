const mix = require('laravel-mix');

mix.js('resources/js/app.js', 'js')
   .postCss('resources/css/app.css', 'css', [
       require('tailwindcss'),
   ])
   .options({
       processCssUrls: false,
       // Enable minification for CSS and JS
       cssNano: {
        discardComments: { removeAll: true },
        normalizeWhitespace: true,
    //     // Disable potentially problematic transforms
    //     colormin: false,
    //     mergeIdents: false,
    //     reduceIdents: false,
    //     zindex: false
    //    },
    //    terser: {
    //        // JS minification options
    //        extractComments: false,
    //        terserOptions: {
    //            compress: {
    //                drop_console: true
    //            }
    //        }
       }
   })
   .setPublicPath('public');

mix.minify('public/css/app.css');
// mix.minify('public/js/app.js');