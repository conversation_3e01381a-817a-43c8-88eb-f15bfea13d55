<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<!-- Skip to content link for accessibility -->
<a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-white focus:font-medium focus:rounded focus:shadow-md focus:outline-none">
    Skip to content
</a>

<!-- include main-menu-panel -->
<?php get_template_part('template-parts/main-menu-panel'); ?>

<div class="bg-white shadow-md w-full sticky top-0 left-0 z-20">
    <header class="bg-stone-300 md:h-[70px] flex items-center justify-center">
        <div class="container px-4 flex justify-between items-center">
            <?php if(get_field('top_bar_email', 'option')) : ?>
                <a href="mailto:<?php echo get_field('top_bar_email', 'option'); ?>" class="truncate hover:text-primary hover:underline flex flex-row gap-2 items-center">
                    <?php echo get_svg_icon('mail', 'h-4 w-4 fill-text'); ?>
                    <?php echo get_field('top_bar_email', 'option'); ?>
                </a>
            <?php endif; ?>
    
            <div class="flex justify-between items-center gap-4">
                <nav class="hidden md:block">
                    <?php
                    wp_nav_menu([
                        'theme_location' => 'topbar',
                        'container' => false,
                        'menu_class' => 'flex space-x-4',
                        'fallback_cb' => false,
                    ]);
                    ?>
                </nav>
    
                <button id="open-side-panel" class="flex flex-row items-center gap-2 text-gray-800 hover:text-primary focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md p-2" aria-expanded="false" aria-controls="side-panel" aria-label="Open menu">
                    <?php echo get_svg_icon('menu', 'h-8 w-8'); ?>
                    <span>Menu</span>
                </button>
            </div>
        </div>
    </header>
    
    <div class="bg-white w-full md:h-[170px] flex items-center justify-center">
        <div class="container px-4 py-2 flex justify-center md:justify-between items-center gap-4">
            <?php if (has_custom_logo()) : ?>
                <?php the_custom_logo(); ?>
            <?php else : ?>
                <a href="<?php echo esc_url(home_url('/')); ?>" class="text-2xl font-bold text-gray-800">
                    <?php bloginfo('name'); ?>
                </a>
            <?php endif; ?>
    
            <?php if(get_field('menu_panel_phone_number', 'option') || get_field('menu_panel_mobile_phone_number', 'option')) : ?>
            <div class="hidden md:flex flex-row gap-4 order-1 md:order-2 p-4 text-center">
                <?php if(get_field('menu_panel_mobile_phone_number', 'option')) :
                    $phone = get_field('menu_panel_mobile_phone_number', 'option');
                    $text = get_field('menu_panel_mobile_phone_number_text', 'option'); ?>
                    <a href="tel:<?php echo spaceless($phone); ?>" class="w-full min-h-8 rounded py-2 px-4 text-white flex flex-col justify-center items-center bg-secondary hover:bg-opacity-90">
                        <span class="text-xs font-medium block uppercase"><?php echo $text; ?></span>
                        <span class="text-lg font-medium"><?php echo $phone; ?></span>
                    </a>
                <?php endif; ?>
    
                <?php if(get_field('menu_panel_phone_number', 'option')) :
                    $phone = get_field('menu_panel_phone_number', 'option'); ?>
                    <a href="tel:<?php echo spaceless($phone); ?>" class="w-full min-h-8 rounded py-2 px-4 text-white flex flex-col justify-center items-center bg-primary hover:bg-opacity-90">
                        <span class="flex flex-row gap-3 text-lg font-medium"><?php echo get_svg_icon('phone', 'h-6 w-6 fill-white'); ?> <?php echo $phone; ?></span>
                    </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<main id="main-content" tabindex="-1">
<?php // do not show if its an archive page
if (!is_front_page() && !is_404() && !is_singular('people') && !is_archive('people')) {
    get_template_part('template-parts/banner');
}
?>