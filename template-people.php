<?php
/**
 * Template Name: People Page
 *
 * The template for displaying the People page
 *
 * @package Marketing_Lawyers
 */

get_header();

// Get current page number for pagination
$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;

// Get the current page URL for pagination (before the loop)
$current_page_url = get_permalink();

// Get published people posts with pagination
$people_query = new WP_Query([
    'post_type' => 'people',
    'post_status' => 'publish',
    'posts_per_page' => 9,
    'paged' => $paged,
    'orderby' => 'title',
    'order' => 'ASC'
]);
?>



<div class="py-8">
    <div class="container mx-auto px-4">
        <?php if ($people_query->have_posts()) { ?>
            <!-- Team Members Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php while ($people_query->have_posts()) {
                    $people_query->the_post();

                    $people_card_image = get_field('people_card_image');

                    if (!$people_card_image) {
                        // Fallback to global fallback card image if available
                        if (function_exists('get_field') && get_field('people_card_image_fallback', 'option')) {
                            $people_card_image = get_field('people_card_image_fallback', 'option');
                        }
                    }
                ?>
                    
                    <div id="person-<?php the_ID(); ?>" <?php post_class('bg-white rounded-lg shadow-md overflow-hidden'); ?>>
                        <div class="aspect-square">
                            <?php if ($people_card_image && isset($people_card_image['url'])) { ?>
                                <img src="<?php echo esc_url($people_card_image['url']); ?>" alt="<?php echo esc_attr($people_card_image['alt'] ?? ''); ?>" class="w-full h-full object-cover">
                            <?php } else { ?>
                                <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                    <span class="text-gray-500 text-sm">No image available</span>
                                </div>
                            <?php } ?>
                        </div>

                        <div class="p-6">
                            <h2 class="text-xl font-bold mb-1">
                                <a href="<?php the_permalink(); ?>" class="hover:text-primary">
                                    <?php the_title(); ?>
                                </a>
                            </h2>

                            <?php
                                // Display job title if ACF field exists
                                if (function_exists('get_field') && get_field('job_title')) {
                                    ?>
                                <div class="mb-4 font-medium">
                                    <?php echo get_field('job_title'); ?>
                                </div>
                            <?php } ?>

                            <div class="text-sm max-w-none mb-4">
                                <?php the_excerpt(); ?>
                            </div>

                            <div class="flex justify-end">
                                <a href="<?php the_permalink(); ?>" class="min-h-8 rounded py-2 px-4 text-white flex flex-row justify-center items-center gap-2 bg-primary hover:bg-opacity-90">
                                View Profile <span class="sr-only">of <?php the_title(); ?></span> <?php echo get_svg_icon('arrow-right', 'h-4 w-4 fill-white'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            </div>

            <!-- Pagination -->
            <div class="mt-12">
                <div class="pagination flex justify-center gap-2">
                    <?php
                    echo paginate_links([
                        'base' => $current_page_url . 'page/%#%/',
                        'format' => '',
                        'current' => max(1, $paged),
                        'total' => $people_query->max_num_pages,
                        'prev_text' => '← Previous',
                        'next_text' => 'Next →',
                        'type' => 'list',
                        'end_size' => 3,
                        'mid_size' => 3,
                    ]);
                    ?>
                </div>
            </div>
        <?php } else { ?>
            <div class="text-center py-12">
                <h2 class="text-2xl font-bold mb-4">No team members found</h2>
                <p class="text-gray-600">Check back later to meet our team.</p>
            </div>
        <?php } ?>

        <?php
        // Reset post data after custom query
        wp_reset_postdata();
        ?>
    </div>
</div>

<?php get_footer(); ?>