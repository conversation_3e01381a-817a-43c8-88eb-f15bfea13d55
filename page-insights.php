<?php
/**
 * Template Name: Insights
 *
 * The template for displaying the blog/insights archive
 */
get_header();
?>

<div class="py-8">
    <div class="container mx-auto px-4">
        <!-- Categories Filter -->
        <div class="mb-12">
            <div class="flex flex-wrap justify-center gap-3 mb-8">
                <a href="<?php echo get_permalink(get_option('page_for_posts')); ?>" class="px-4 py-2 bg-secondary text-white rounded-full hover:bg-opacity-90 transition-colors">
                    All
                </a>
                <?php
                $categories = get_categories();
foreach ($categories as $category) {
    ?>
                    <a href="<?php echo get_category_link($category->term_id); ?>" class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 transition-colors">
                        <?php echo $category->name; ?>
                    </a>
                <?php } ?>
            </div>
        </div>

        <!-- All Posts -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php
    $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
$posts_query = new WP_Query([
    'post_type' => 'post',
    'posts_per_page' => 9,
    'paged' => $paged,
    'meta_query' => [
        [
            'key' => '_is_featured',
            'compare' => 'NOT EXISTS',
        ],
    ],
]);

if ($posts_query->have_posts()) {
    while ($posts_query->have_posts()) {
        $posts_query->the_post();
        ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class('bg-white rounded overflow-hidden'); ?>>
                    <?php if (has_post_thumbnail()) { ?>
                        <div class="aspect-w-16 aspect-h-9">
                            <?php the_post_thumbnail('medium_large', ['class' => 'w-full h-full object-cover']); ?>
                        </div>
                    <?php } ?>

                    
                    <div class="flex flex-col gap-4 p-4">
                        <div>
                            <?php echo get_the_date(); ?>
                        </div>

                        <h2 class="text-xl font-bold">
                            <a href="<?php the_permalink(); ?>" class="hover:text-primary">
                                <?php the_title(); ?>
                            </a>
                        </h2>

                        <p class="font-bold"><?php the_author(); ?></p>

                        <a href="<?php the_permalink(); ?>" class="w-full min-h-8 rounded py-2 px-4 text-text flex flex-row justify-center items-center gap-2 bg-quantiary hover:bg-opacity-90">
                            Read more <span class="sr-only">about <?php the_title(); ?></span> <?php echo get_svg_icon('arrow-right', 'h-4 w-4 fill-text'); ?>
                        </a>
                    </div>
                </article>
            <?php
    }
    ?>
        </div>

        <!-- Pagination -->
        <div class="mt-12">
            <div class="pagination flex justify-center gap-2">
                <?php
                    echo paginate_links([
                        'base' => str_replace(999999999, '%#%', esc_url(get_pagenum_link(999999999))),
                        'format' => '?paged=%#%',
                        'current' => max(1, get_query_var('paged')),
                        'total' => $posts_query->max_num_pages,
                        'prev_text' => '← Previous',
                        'next_text' => 'Next →',
                        'type' => 'list',
                        'end_size' => 3,
                        'mid_size' => 3,
                    ]);
                ?>
            </div>
        </div>
        <?php
} else {
    ?>
            <div class="text-center py-12">
                <h2 class="text-2xl font-bold mb-4">No posts found</h2>
                <p class="text-gray-600">Check back later for new content.</p>
            </div>
        <?php
}
wp_reset_postdata();
?>
    </div>
</div>

<?php get_footer(); ?>