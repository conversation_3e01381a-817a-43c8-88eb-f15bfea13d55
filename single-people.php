<?php
/**
 * The template for displaying single 'people' custom post type entries.
 */
get_header();

// Start the WordPress loop
while (have_posts()) {
    the_post();

    // Get all the ACF fields we need
    $job_title = function_exists('get_field') ? get_field('job_title') : '';
    $social_accounts = function_exists('get_field') ? get_field('social_accounts') : array();
    $email = function_exists('get_field') ? get_field('email') : '';
    $phone = function_exists('get_field') ? get_field('telephone') : '';
    $base_office = function_exists('get_field') ? get_field('base_office') : '';
    $qualified = function_exists('get_field') ? get_field('qualified') : '';

?>

<section class="flex justify-start md:pt-[150px] flex-col w-full relative bg-[#EADDCB] after:content[''] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[200px] after:w-full after:bg-white">
    <div class="w-full max-w-5xl mx-auto bg-tertiary z-10 md:h-[400px] mx-4 flex flex-col md:flex-row">
        <div class="p-6 md:p-12 flex-grow flex flex-col gap-4 justify-between">

            <span class="uppercase">Profile</span>

            <div>
                <h1 class="text-3xl font-bold mb-2">
                    <?php the_title(); ?>
                </h1>
                <?php if ($job_title) { ?>
                    <p class="mb-6">
                        <?php echo $job_title; ?>
                    </p>
                <?php } ?>
    
                <?php if ($phone || $email) : ?>
                    <div class="flex flex-col space-y-2 font-bold">
                        <span class="sr-only">Contact Information</span>
                        <?php if ($phone) : ?>
                            <div class="flex items-center mb-2">
                                <?php echo get_svg_icon('phone', 'h-5 w-5 text-gray-500 mr-2'); ?>
                                <a href="tel:<?php echo esc_html(spaceless($phone)); ?>" class="hover:underline hover:text-primary">
                                    <?php echo esc_html($phone); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        <?php if ($email) : ?>
                            <div class="flex items-center">
                                <?php echo get_svg_icon('mail', 'h-5 w-5 text-gray-500 mr-2'); ?>
                                <a href="mailto:<?php echo esc_attr($email); ?>" class="hover:underline hover:text-primary">
                                    <?php echo esc_html($email); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="flex flex-row gap-2">
                <?php
                if ($social_accounts && is_array($social_accounts) && count($social_accounts) > 0) {
                    foreach ($social_accounts as $account) {
                        if (empty($account['social_link'])) {
                            continue;
                        }

                        $network = isset($account['social_network']) ? $account['social_network'] : '';
                        $link = $account['social_link'];
                        $hover_class = '';

                        switch ($network) {
                            case 'linkedin':
                                $icon = get_svg_icon('linkedin', 'h-12 w-12');
                                $hover_class = 'hover:text-blue-600';
                                break;
                            case 'twitter':
                            case 'x':
                                $icon = get_svg_icon('twitter', 'h-12 w-12');
                                $hover_class = 'hover:text-blue-400';
                                break;
                            case 'facebook':
                                $icon = get_svg_icon('facebook', 'h-12 w-12');
                                $hover_class = 'hover:text-blue-600';
                                break;
                            case 'youtube':
                                $icon = get_svg_icon('youtube', 'h-12 w-12');
                                $hover_class = 'hover:text-red-600';
                                break;
                            default:
                                $icon = '';
                                break;
                        }

                        if ($icon) {
                            ?>
                            <a href="<?php echo esc_url($link); ?>" class="text-gray-500 <?php echo $hover_class; ?>" target="_blank" rel="noopener noreferrer" aria-label="<?php echo esc_attr(ucfirst($network)); ?>">
                                <?php echo $icon; ?>
                            </a>
                            <?php
                        }
                    }
                }
                ?>
            </div>
        </div>

        <div class="aspect-square h-full">
            <?php if (has_post_thumbnail()) : ?>
                <?php the_post_thumbnail('large', ['class' => 'w-full h-full object-cover']); ?>
            <?php elseif (function_exists('get_field') && get_field('people_card_image')) : ?>
                <?php $people_card_image = get_field('people_card_image'); ?>
                <?php echo wp_get_attachment_image($people_card_image['ID'], 'large', false, ['class' => 'w-full h-full object-cover']); ?>
            <?php else : ?>
                <?php if (function_exists('get_field') && get_field('people_card_image_fallback', 'option')) {
                    $people_card_image = get_field('people_card_image_fallback', 'option');
                    echo wp_get_attachment_image($people_card_image['ID'], 'large', false, ['class' => 'w-full h-full object-cover']);
                }; ?>
            <?php endif; ?>
        </div>
    </div>
</section>

<div class="container mx-auto px-4">
    <div class="py-4">
        <?php echo get_url_based_breadcrumbs(); ?>
    </div>
</div>

<div class="container mx-auto px-4 pb-12">
    <article id="person-<?php the_ID(); ?>" <?php post_class('flex flex-col md:flex-row gap-6'); ?>>
        <div class="flex-grow">
            <?php the_content(); ?>
        </div>

        <aside class="min-w-[250px] flex flex-col gap-4">

            <?php if ($base_office) : ?>
                <div class="flex flex-row items-center gap-2 font-bold">
                    <?php echo get_svg_icon('pin', 'h-8 w-8 fill-primary'); ?>
                    <a href="<?php echo esc_url(get_permalink($base_office[0]->ID)); ?>" class="hover:underline hover:text-primary">
                        <?php echo esc_html($base_office[0]->post_title); ?>
                    </a>
                </div>
            <?php endif; ?>


            <?php if ($qualified) : ?>
                <div class="flex flex-row items-center gap-2 font-bold">
                    <?php echo get_svg_icon('star', 'h-8 w-8 fill-primary'); ?>
                    <span>Qualified <?php echo esc_html($qualified); ?></span>
                </div>
            <?php endif; ?>
        </aside>
    </article>
</div>

<?php
} // End of the WordPress loop
get_footer(); ?>