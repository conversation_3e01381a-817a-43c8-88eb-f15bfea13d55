<?php
/**
 * Template Name: Homepage
 *
 * The template for displaying the homepage
 */
get_header();
?>

<!-- Get hero fields -->
 <?php
 $hero = get_field('hero');
 $has_featured_image = has_post_thumbnail();

if ($has_featured_image) {
    // Get the featured image URL
    $featured_img_url = get_the_post_thumbnail_url(get_the_ID(), 'full');
}
 ?>

<section class="relative bg-[#EADDCB] py-16 bg-cover bg-center flex items-center justify-center bg-no-repeat bg-cover bg-no-repeat " style="background-image: url('<?php echo esc_url($featured_img_url); ?>');">
    <div class="container flex items-center justify-center md:justify-start p-4">
        <div class="relative z-10 bg-white rounded-xl px-6 py-8 flex flex-col gap-4 max-w-[500px]">
            <h1 class="text-3xl md:text-4xl mb-3"><?php echo $hero['hero_title'] ?? the_title(); ?></h1>
            <?php echo $hero['hero_content_intro'] ?? ''; ?>
            <div class="flex flex-col sm:flex-row gap-4 mt-8 md:mt-16">
                <a href="tel:01933273400" class="bg-primary text-white hover:bg-opacity-90 px-6 py-3 rounded font-medium transition-colors flex flex-row items-center justify-center gap-2"><?php echo get_svg_icon('phone', 'h-4 w-4 fill-white'); ?> 01933 273 400</a>
                <?php if($hero['hero_intro_link']) : ?>
                    <a href="<?php echo esc_url($hero['hero_intro_link']['url']); ?>" class="bg-white text-primary hover:text-white hover:bg-primary border-2 border-primary px-6 py-3 rounded font-medium transition-colors flex flex-row items-center gap-2 justify-center">
                        <?php echo esc_html($hero['hero_intro_link']['title']); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<div class="bg-white flex flex-col md:flex-row min-h-[300px]">
    <div class="basis-[50%] md:max-w-1/2 flex items-center justify-center p-6 md:p-12 md:pl-[calc((100%-768px)/2)] lg:pl-[calc((100%-1024px)/2)] xl:pl-[calc((100%-1280px)/2)] 2xl:pl-[calc((100%-1536px)/2)] text-center md:text-left md:max-w-[50%]">
        <h2 class="text-3xl md:text-4xl uppercase">Legal support you can count on, <span class="underline decoration-quantiary decoration-4 underline-offset-8">every time.</span></h2>
    </div>
    <div class="content basis-[50%] md:max-w-1/2 bg-[#EADDCB] flex flex-col p-6 md:p-12 md:pr-[calc((100%-768px)/2)] lg:pr-[calc((100%-1024px)/2)] xl:pr-[calc((100%-1280px)/2)] 2xl:pr-[calc((100%-1536px)/2)] text-center md:text-left md:max-w-[50%]">
        <?php the_content(); ?>
    </div>

</div>

<?php
$services_group = get_field('services');
$services = isset($services_group['service']) ? $services_group['service'] : [];
?>

<?php if (!empty($services)) : ?>
    <div id="services" class="pt-16 bg-white">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold mb-4">Our Services</h2>
        </div>
        <?php foreach ($services as $service) :
                $image = $service['service_image'];
                $content = $service['service_content'];
                $image_side = $service['service_image_side'];
                $link = $service['service_link'];
                $reverse = ($image_side === 'right') ? 'order-3' : 'order-1';
            ?>
            <div class="bg-tertiary">
                <div class="container mx-auto md:min-h-[400px] flex flex-col md:flex-row relative">
                    <div class="hidden md:block overflow-hidden md:basis-[50%] md:max-w-1/2 z-10 <?php echo $reverse; ?>">
                        <img src="<?php echo esc_url($image['url']); ?>" class="aspect-video w-full h-auto md:aspect-auto md:absolute max-w-full md:w-[50vw] <?php echo $image_side === 'right' ? '' : 'md:right-[50%]'; ?> md:h-full object-center object-cover" alt="">
                    </div>
                    <div class="md:basis-[50%] bg-tertiary p-4 md:p-12 order-2 flex flex-col gap-4 justify-between content">
                        <div><?php echo $content; ?></div>

                        <div class="flex flex-col md:flex-row gap-4">
                        <a href="tel:01933273400" class="!no-underline bg-primary !text-white hover:bg-opacity-90 px-6 py-3 rounded font-medium transition-colors flex flex-row items-center justify-center gap-2"><?php echo get_svg_icon('phone', 'h-4 w-4 fill-white'); ?> 01933 273 400</a>
                        <?php if (!empty($link)) : ?>
                            <a href="<?php echo esc_url($link['url']); ?>" target="<?php echo esc_attr($link['target']); ?>" class="group !no-underline bg-tertiary !text-primary hover:!text-white hover:bg-primary border-2 border-primary px-6 py-3 rounded font-medium transition-colors flex flex-row items-center gap-2 justify-center">
                                <?php echo esc_html($link['title']); ?> <?php echo get_svg_icon('arrow-right', 'h-4 w-4 fill-primary group-hover:fill-white transition-colors '); ?>
                            </a>
                        <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>



<?php
$testimonial = get_field('testimonial');
?>
<?php if($testimonial): ?>

<div class="bg-tertiary flex flex-col items-center justify-center px-4 py-8 relative overflow-hidden">
    <div class="absolute top-0 left-0 w-full h-full flex items-center justify-start opacity-10">
        <?php echo get_svg_icon('quote', 'h-full w-auto fill-[#666666]'); ?>
    </div>

    <div class="container min-h-[400px] relative z-10 flex flex-col items-center justify-center h-full">
        <blockquote class="font-bold text-xl md:text-2xl py-8">
           <?php echo esc_html($testimonial); ?>
        </blockquote>
    </div>
    <a href="/testimonials" class="z-10 self-end bg-primary mt-6 text-white hover:bg-opacity-90 px-6 py-3 rounded font-medium transition-colors flex flex-row items-center justify-center gap-2">
        See all testimonials <?php echo get_svg_icon('arrow-right', 'h-4 w-4 fill-white group-hover:fill-white transition-colors '); ?>
    </a>
</div>
<?php endif; ?>

<div id="contact" class="py-16">
    <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto p-8">
            <?php echo do_shortcode('[contact-form-7 id="110" title="Contact form"]'); ?>
        </div>
    </div>
</div>

<?php
// Display recent blog posts
if (have_posts()) {
    ?>
<div class="py-16 bg-tertiary">
    <div class="container mx-auto px-4">
        <div class="mb-12">
            <h2 class="text-3xl font-bold mb-4">Our Insights</h2>
        </div>

        <div class="flex flex-col gap-4">
            <?php
                $recent_posts = new WP_Query([
                    'post_type' => 'post',
                    'posts_per_page' => 3,
                    'ignore_sticky_posts' => 1,
                ]);

    while ($recent_posts->have_posts()) {
        $recent_posts->the_post();
        ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class('flex flex-col md:flex-row gap-8 w-full md:items-center py-6 md:py-8 border-[#777777] md:border-b md:last:border-none'); ?>>
                    <?php if (has_post_thumbnail()) { ?>
                        <div class="aspect-square md:h-[150px]">
                            <?php the_post_thumbnail('medium_large', ['class' => 'w-full h-full object-cover']); ?>
                        </div>
                    <?php } else { ?>
                        <div class="aspect-square md:h-[150px] bg-gray-200 flex items-center justify-center">
                            <span class="text-gray-500">No Image</span>
                        </div>
                    <?php } ?>

                    <div class="flex flex-col flex-grow gap-4 justify-between">
                        <div class="text-sm">
                            <?php echo get_the_date(); ?>
                        </div>

                        <h3 class="text-xl font-bold">
                            <a href="<?php the_permalink(); ?>" class="uppercase hover:text-primary max-w-[600px]">
                                <?php the_title(); ?>
                            </a>
                        </h3>

                        <div class="text-sm font-bold">
                            <?php the_author(); ?>
                        </div>

                    </div>

                    <a href="<?php the_permalink(); ?>" class="min-h-8 rounded py-2 px-4 text-text flex flex-row justify-center items-center gap-2 bg-quantiary hover:bg-opacity-90 min-w-[140px]">
                        Read more <span class="sr-only">about <?php the_title(); ?></span> <?php echo get_svg_icon('arrow-right', 'h-4 w-4 fill-text'); ?>
                    </a>
                </article>
            <?php
    }
    wp_reset_postdata();
    ?>
        </div>
    </div>
</div>
<?php } ?>

<div class="py-16 bg-white">
    <div class="container mx-auto px-4">
    <div class="flex flex-row flex-wrap gap-4 items-center justify-center">
        <?php

// Check rows exists.
if( have_rows('trust_logos') ):

    // Loop through rows.
    while( have_rows('trust_logos') ) : the_row();

        // Load sub field value.
        $logo = get_sub_field('trust_logo');
        $link = get_sub_field('trust_logo_link');
        
        // Check for logo and link.
        if( $logo && $link ): ?>
            <a href="<?php echo esc_url($link); ?>" target="_blank" rel="noopener noreferrer">
                <img src="<?php echo esc_url($logo['url']); ?>" alt="<?php echo esc_attr($logo['alt']); ?>" class="h-24">
            </a>
        <?php else: ?>
            <img src="<?php echo esc_url($logo['url']); ?>" alt="<?php echo esc_attr($logo['alt']); ?>" class="h-24">
        <?php endif;

    // End loop.
    endwhile; endif;?>
    </div>
    </div>

</div>

<?php get_footer(); ?>