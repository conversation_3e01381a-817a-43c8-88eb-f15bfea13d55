<?php
/**
 * The template for displaying the 'people' custom post type archive.
 */
get_header();

// Custom banner for the people archive page
?>
<section class="w-full md:h-[285px] bg-[#EADDCB] relative">
    <div class="relative bg-[#EADDCB] h-full w-full md:w-1/2 z-10 after:hidden md:after:block after:content[''] after:[clip-path:polygon(0_0,100%_0,0%_100%,0_100%)] after:bg-[#EADDCB] after:absolute after:top-0 after:-right-[44px] after:bottom-0 after:h-full after:w-[45px] after:overflow-hidden"></div>
    <div class="md:absolute w-full z-10 top-0">
        <div class="pl-0 md:pl-[calc((100%-768px)/2)] lg:pl-[calc((100%-1024px)/2)] xl:pl-[calc((100%-1280px)/2)] 2xl:pl-[calc((100%-1536px)/2)] text-center md:text-left md:max-w-[50%]">
            <h1 class="py-12 px-4 text-3xl md:text-4xl font-bold mb-3">
                Our People
            </h1>
        </div>
    </div>

    <?php
    // Get featured image from options page if available
    $featured_img_url = '';
    if (function_exists('get_field') && get_field('people_archive_image', 'option')) {
        $featured_img = get_field('people_archive_image', 'option');
        $featured_img_url = $featured_img['url'];
    }

    if ($featured_img_url) : ?>
        <div class="block md:absolute inset-0 md:right-0 md:left-auto h-full w-full md:w-1/2">
            <img
            src="<?php echo esc_url($featured_img_url); ?>"
            alt=""
            class="h-full w-full object-cover"
            aria-hidden="true"
            >
        </div>
    <?php endif; ?>
</section>

<div class="container mx-auto px-4">
    <div class="py-4">
        <?php echo get_url_based_breadcrumbs(); ?>
    </div>
</div>

<div class="py-8">
    <div class="container mx-auto px-4">
        <?php if (have_posts()) { ?>
            <!-- Team Members Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php while (have_posts()) {
                    the_post(); 

                    $people_card_image = get_field('people_card_image');

                    if (!$people_card_image) {
                        // Fallback to global fallback card image if available
                        if (function_exists('get_field') && get_field('people_card_image_fallback', 'option')) {
                            $people_card_image = get_field('people_card_image_fallback', 'option');
                        }
                    };
                ?>
                    
                    <div id="person-<?php the_ID(); ?>" <?php post_class('bg-white rounded-lg shadow-md overflow-hidden'); ?>>
                        <div class="aspect-square">
                            <img src="<?php echo esc_url($people_card_image['url']); ?>" alt="<?php echo esc_attr($people_card_image['alt']); ?>" class="w-full h-full object-cover">
                        </div>

                        <div class="p-6">
                            <h2 class="text-xl font-bold mb-1">
                                <a href="<?php the_permalink(); ?>" class="hover:text-primary">
                                    <?php the_title(); ?>
                                </a>
                            </h2>

                            <?php
                                // Display job title if ACF field exists
                                if (function_exists('get_field') && get_field('job_title')) {
                                    ?>
                                <div class="mb-4 font-medium">
                                    <?php echo get_field('job_title'); ?>
                                </div>
                            <?php } ?>

                            <div class="text-sm max-w-none mb-4">
                                <?php the_excerpt(); ?>
                            </div>

                            <div class="flex justify-end">
                                <a href="<?php the_permalink(); ?>" class="min-h-8 rounded py-2 px-4 text-white flex flex-row justify-center items-center gap-2 bg-primary hover:bg-opacity-90">
                                View Profile <span class="sr-only">of <?php the_title(); ?></span> <?php echo get_svg_icon('arrow-right', 'h-4 w-4 fill-white'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            </div>

            <!-- Pagination -->
            <div class="mt-12">
                <div class="pagination flex justify-center gap-2">
                    <?php
                    echo paginate_links([
                        'prev_text' => '← Previous',
                        'next_text' => 'Next →',
                        'type' => 'plain',
                        'end_size' => 3,
                        'mid_size' => 3,
                    ]);
                    ?>
                </div>
                </div>
            </div>
        <?php } else { ?>
            <div class="text-center py-12">
                <h2 class="text-2xl font-bold mb-4">No team members found</h2>
                <p class="text-gray-600">Check back later to meet our team.</p>
            </div>
        <?php } ?>
    </div>
</div>

<?php get_footer(); ?>