<?php get_header(); ?>

<div class="container mx-auto px-4 py-12">
    <?php if (have_posts()) { ?>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php while (have_posts()) {
                the_post(); ?>
                 <article id="post-<?php the_ID(); ?>" <?php post_class('bg-white rounded overflow-hidden'); ?>>
                    <?php if (has_post_thumbnail()) { ?>
                        <div class="aspect-w-16 aspect-h-9">
                            <?php the_post_thumbnail('medium_large', ['class' => 'w-full h-full object-cover']); ?>
                        </div>
                    <?php } ?>

                    
                    <div class="flex flex-col gap-4 p-4">
                        <div>
                            <?php echo get_the_date(); ?>
                        </div>

                        <h2 class="text-xl font-bold">
                            <a href="<?php the_permalink(); ?>" class="hover:text-primary">
                                <?php the_title(); ?>
                            </a>
                        </h2>

                        <p class="font-bold"><?php the_author(); ?></p>

                        <a href="<?php the_permalink(); ?>" class="w-full min-h-8 rounded py-2 px-4 text-text flex flex-row justify-center items-center gap-2 bg-quantiary hover:bg-opacity-90">
                            Read more <span class="sr-only">about <?php the_title(); ?></span> <?php echo get_svg_icon('arrow-right', 'h-4 w-4 fill-text'); ?>
                        </a>
                    </div>
                </article>
            <?php } ?>
        </div>
        
        <div class="mt-12">
            <?php the_posts_pagination([
                'prev_text' => '← Previous',
                'next_text' => 'Next →',
                'class' => 'flex justify-center space-x-2',
            ]); ?>
        </div>
    <?php } else { ?>
        <div class="text-center py-12">
            <h2 class="text-2xl font-bold mb-4">No posts found</h2>
            <p class="text-gray-600">Try searching for something else or check back later.</p>
        </div>
    <?php } ?>
</div>

<?php get_footer(); ?>

