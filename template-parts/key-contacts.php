<?php
/**
 * Template part for displaying key contacts
 *
 * @package Marketing_Lawyers
 */

$key_contact_ids = get_field('key_contacts');

if ($key_contact_ids && is_array($key_contact_ids) && !empty($key_contact_ids)) :
    ?>
    <h2 class="text-3xl font-bold mb-4 uppercase">Key Contacts</h2>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <?php
        foreach ($key_contact_ids as $contact_id) {
            $contact_post = get_post($contact_id);

            if (!$contact_post) {
                continue;
            }

            // Try to get the result_card_image first
            $result_card_image = get_field('result_card_image', $contact_id);

            // If no result_card_image, fall back to featured image
            $featured_image_id = get_post_thumbnail_id($contact_id);
            $job_title = get_field('job_title', $contact_id);
            $permalink = get_permalink($contact_id);
            ?>
            <div class="flex flex-row bg-tertiary rounded-md overflow-hidden">
                <div class="aspect-square w-[200px]">
                    <?php
                    if ($result_card_image) {
                        // Use result_card_image if available
                        echo wp_get_attachment_image($result_card_image['ID'], 'medium_large', false, ['class' => 'w-full h-full object-cover']);
                    } elseif ($featured_image_id) {
                        // Fall back to featured image if available
                        echo wp_get_attachment_image($featured_image_id, 'medium_large', false, ['class' => 'w-full h-full object-cover']);
                    } else {
                        // Fallback if no image is available
                        echo '<div class="w-full h-full bg-gray-200 flex items-center justify-center"><span class="text-gray-400">No image</span></div>';
                    }
                    ?>
                </div>
                <div class="p-6">
                    <h2 class="text-xl font-bold mb-1">
                        <?php echo esc_html($contact_post->post_title); ?>
                    </h2>
                    <?php if ($job_title) : ?>
                    <div class="text-primary font-medium mb-4">
                        <?php echo esc_html($job_title); ?>
                    </div>
                    <?php endif; ?>
                    <a href="<?php echo esc_url($permalink); ?>" class="group flex flex-row items-center gap-2  hover:text-primary">
                        View Profile <?php echo get_svg_icon('arrow-right', 'group:hover:fill-primary h-4 w-4 fill-text'); ?>
                    </a>
                </div>
            </div>
        <?php
        }
        ?>
    </div>
<?php
endif;
?>