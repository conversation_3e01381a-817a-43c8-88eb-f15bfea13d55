<?php
/**
 * Template part for displaying the page banner
 *
 * @package Marketing_Lawyers
 */

// Get the page title
$banner_title = get_the_title();

// Check if the page has a featured image
$has_featured_image = has_post_thumbnail();

if ($has_featured_image) {
    // Get the featured image URL
    $featured_img_url = get_the_post_thumbnail_url(get_the_ID(), 'full');
} else {
    // Get the fallback image from the options page if available
    $fallback_image = function_exists('get_field') && get_field('global_fallback_banner', 'option');

    if ($fallback_image) {
        $featured_img = get_field('global_fallback_banner', 'option');
        $featured_img_url = $featured_img['url'];
    }
}   
?>

<section class="w-full md:h-[285px] bg-[#EADDCB] relative">
    <div class="relative bg-[#EADDCB] h-full w-full md:w-1/2 z-10 after:hidden md:after:block after:content[''] after:[clip-path:polygon(0_0,100%_0,0%_100%,0_100%)] after:bg-[#EADDCB] after:absolute after:top-0 after:-right-[44px] after:bottom-0 after:h-full after:w-[45px] after:overflow-hidden"></div>
    <div class="md:absolute w-full z-10 top-0">
        <div class="pl-0 md:pl-[calc((100%-768px)/2)] lg:pl-[calc((100%-1024px)/2)] xl:pl-[calc((100%-1280px)/2)] 2xl:pl-[calc((100%-1536px)/2)] text-center md:text-left md:max-w-[50%]">
            <h1 class="py-12 px-4 text-3xl md:text-4xl font-bold  mb-3">
                <?php the_title(); ?>
            </h1>
        </div>
    </div>
    
   
    <?php if (!empty($featured_img_url)) : ?>
        <div class="block md:absolute inset-0 md:right-0 md:left-auto h-full w-full md:w-1/2">
            <img 
            src="<?php echo esc_url($featured_img_url); ?>" 
            alt="" 
            class="h-full w-full object-cover"
            aria-hidden="true"
            >
        </div>
    <?php endif; ?>
</section>

<div class="container mx-auto px-4">
    <div class="py-4">
        <?php echo get_url_based_breadcrumbs(); ?>
    </div>
</div>