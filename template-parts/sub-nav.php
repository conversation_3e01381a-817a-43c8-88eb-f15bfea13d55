<?php
global $post;

$current_id = $post->ID;
$parent_id = wp_get_post_parent_id($current_id);

// Try to get children of current page
$children = get_pages([
    'child_of' => $current_id,
    'sort_column' => 'menu_order',
    'sort_order' => 'ASC',
]);

// If no children, fallback to siblings
if (empty($children) && $parent_id) {
    $children = get_pages([
        'child_of' => $parent_id,
        'sort_column' => 'menu_order',
        'sort_order' => 'ASC',
    ]);
}

// Filter out hidden pages
$filtered_pages = array_filter($children, function ($page) {
    return !get_field('hide_in_subnav', $page->ID);
});
?>

<?php if (!empty($filtered_pages)) : ?>
    <nav class="sub-nav mb-6">
        <h3 class="text-2xl uppercase font-bold mb-4">
            <?php echo (in_array(17, array_column(get_ancestors($current_id, 'page'), 'ID'))) ? 'Sub nav' : 'Our services'; ?>
        </h3>
        <ul class="flex flex-col gap-4">
            <?php foreach ($filtered_pages as $page) : ?>
                <li>
                    <a 
                        href="<?php echo get_permalink($page->ID); ?>" 
                        class="flex flex-row gap-2 items-center <?php echo $page->ID === $current_id ? 'font-bold text-primary' : 'font-bold hover:text-primary'; ?>">
                        <?php echo esc_html($page->post_title); ?> <?php echo get_svg_icon('arrow-right', 'h-4 w-4 fill-primary'); ?>
                    </a>
                </li>
            <?php endforeach; ?>
        </ul>
    </nav>
<?php endif; ?>