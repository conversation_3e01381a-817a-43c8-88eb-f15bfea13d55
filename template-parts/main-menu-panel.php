<!-- Overlay for side panel -->
<div id="side-panel-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden transition-opacity duration-300 ease-in-out" aria-hidden="true" tabindex="-1"></div>

<!-- Side Panel Navigation -->
<div id="side-panel" class="fixed top-0 right-0 w-[90%] md:w-[600px] lg:w-[700px] h-full bg-white shadow-lg z-50 transform translate-x-full transition-transform duration-300 ease-in-out" role="dialog" aria-modal="true" aria-hidden="true" aria-labelledby="side-panel-title" inert>
    <div class="h-full flex flex-col overflow-y-auto p-4 md:p-12 lg:p-14">
        <div class="flex items-center justify-end p-4">
            <h2 id="side-panel-title" class="sr-only">Menu</h2>
            <button id="close-side-panel" class="flex flex-row justify-center items-center gap-4  hover:text-primary focus:outline-none focus:ring-2 focus:ring-primary rounded-md p-2" aria-label="Close menu">
                <span class="text-xl font-medium">Close menu</span>
                <?php echo get_svg_icon('close', 'h-12 w-12 bg-gray-200 p-2 rounded'); ?>
            </button>
        </div>

        <nav class="p-4 order-2 md:order-1">
            <?php
            wp_nav_menu([
                'theme_location' => 'primary',
                'container' => false,
                'menu_class' => 'flex flex-col',
                'fallback_cb' => false,
                'link_before' => '<span class="block text-xl font-medium hover:text-primary transition-colors focus:outline-none focus:ring-2 focus:ring-primary rounded">',
                'link_after' => '</span>',
            ]);
                ?>
        </nav>

        <?php if(get_field('menu_panel_phone_number', 'option') || get_field('menu_panel_mobile_phone_number', 'option')) : ?>
        <div class="p-4 flex flex-col gap-4 order-1 md:order-2">
            <?php if(get_field('menu_panel_phone_number', 'option')) :
                $phone = get_field('menu_panel_phone_number', 'option'); ?>
                <a href="tel:<?php echo spaceless($phone); ?>" class="w-full min-h-14 rounded py-2 px-4 text-white flex flex-col justify-center items-center bg-primary hover:bg-opacity-90">
                    <span class="flex flex-row gap-3 text-xl font-medium"><?php echo get_svg_icon('phone', 'h-6 w-6 fill-white'); ?> <?php echo $phone; ?></span>
                </a>
            <?php endif; ?>

            <?php if(get_field('menu_panel_mobile_phone_number', 'option')) :
                $phone = get_field('menu_panel_mobile_phone_number', 'option');
                $text = get_field('menu_panel_mobile_phone_number_text', 'option'); ?>
                <a href="tel:<?php echo spaceless($phone); ?>" class="w-full min-h-14 rounded py-2 px-4 text-white flex flex-col justify-center items-center bg-secondary hover:bg-opacity-90">
                    <span class="text-xs font-medium block uppercase"><?php echo $text; ?></span>
                    <span class="text-xl font-medium"><?php echo $phone; ?></span>
                </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</div>