## To run the project
Run `npm run dev` for development build.

For auto-watching during development, run `npm run watch`.

### Production Build with Minification
For production with minified CSS and JS, run:
```bash
npm run prod
```

This will:
- Minify the CSS output (app.css)
- Minify JavaScript files
- Remove comments and whitespace
- Optimize assets for production

## Code Style
This project uses Laravel Pint for PHP code style. To check your code style:

```bash
npm run pint:test
```

To automatically fix code style issues:

```bash
npm run pint
```

## Requirements
- Node v22+
- PHP 8.1+
- Composer