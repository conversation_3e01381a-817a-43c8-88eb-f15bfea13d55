<?php

// Enqueue styles and scripts
function ml_enqueue_assets()
{
    wp_enqueue_style('theme-css', get_template_directory_uri() . '/public/css/app.min.css', [], filemtime(get_template_directory() . '/public/css/app.min.css'));
    wp_enqueue_script('theme-js', get_template_directory_uri() . '/public/js/app.min.js', [], filemtime(get_template_directory() . '/public/js/app.min.js'), true);

    // Enqueue FAQ block styles
    if (file_exists(get_template_directory() . '/includes/blocks/faq/style.css')) {
        wp_enqueue_style(
            'ml-faq-block-style',
            get_template_directory_uri() . '/includes/blocks/faq/style.css',
            [],
            filemtime(get_template_directory() . '/includes/blocks/faq/style.css')
        );
    }
}
add_action('wp_enqueue_scripts', 'ml_enqueue_assets');

// Manifest and icons
function add_favicon_and_meta() {
    echo '<link rel="apple-touch-icon" href="' . esc_url(get_template_directory_uri() . '/assets/apple-touch-icon.png') . '">';
    echo '<link rel="manifest" href="' . esc_url(get_template_directory_uri() . '/assets/site.webmanifest') . '">';
    echo '<meta name="msapplication-TileColor" content="#ffffff">';
}
add_action('wp_head', 'add_favicon_and_meta', 1);

// Theme setup
function ml_setup()
{
    // Add default posts and comments RSS feed links to head
    add_theme_support('automatic-feed-links');

    // Let WordPress manage the document title
    add_theme_support('title-tag');

    // Enable support for Post Thumbnails on posts and pages
    add_theme_support('post-thumbnails');

    // Add support for responsive embeds
    add_theme_support('responsive-embeds');

    // Add support for custom logo
    add_theme_support('custom-logo', [
        'height' => 100,
        'width' => 400,
        'flex-height' => true,
        'flex-width' => true,
    ]);

    // Add support for full and wide align images
    add_theme_support('align-wide');

    // Add support for editor styles
    add_theme_support('editor-styles');

    // Register navigation menus
    register_nav_menus([
        'primary' => __('Primary Menu', 'ml'),
        'topbar' => __('Top bar Menu', 'ml'),
        'footer' => __('Footer Menu', 'ml'),
    ]);
}
add_action('after_setup_theme', 'ml_setup');

// Register widget areas
function ml_widgets_init()
{
    register_sidebar([
        'name' => __('Sidebar', 'ml'),
        'id' => 'sidebar-1',
        'description' => __('Add widgets here to appear in your sidebar.', 'ml'),
        'before_widget' => '<div id="%1$s" class="widget %2$s mb-6">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title text-lg font-bold mb-3">',
        'after_title' => '</h3>',
    ]);

    register_sidebar([
        'name' => __('Footer Left', 'ml'),
        'id' => 'footer-left',
        'description' => __('Add widgets here to appear in the left section of your footer.', 'ml'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="text-xl font-bold mb-4">',
        'after_title' => '</h3>',
    ]);

    register_sidebar([
        'name' => __('Footer Middle', 'ml'),
        'id' => 'footer-middle',
        'description' => __('Add widgets here to appear in the middle section of your footer.', 'ml'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="text-xl font-bold mb-4">',
        'after_title' => '</h3>',
    ]);

    register_sidebar([
        'name' => __('Footer Right', 'ml'),
        'id' => 'footer-right',
        'description' => __('Add widgets here to appear in the right section of your footer.', 'ml'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="text-xl font-bold mb-4">',
        'after_title' => '</h3>',
    ]);

    register_sidebar([
        'name' => __('Footer Smallprint', 'ml'),
        'id' => 'footer-smallprint',
        'description' => __('Add widgets here to appear in the footer smallprint section (copyright, legal links, etc.).', 'ml'),
        'before_widget' => '<div id="%1$s" class="widget %2$s inline-block mx-2">',
        'after_widget' => '</div>',
        'before_title' => '<span class="sr-only">',
        'after_title' => '</span>',
    ]);
}
add_action('widgets_init', 'ml_widgets_init');

// Customize excerpt length
function ml_excerpt_length($length)
{
    return 20;
}
add_filter('excerpt_length', 'ml_excerpt_length');

// Customize excerpt more string
function ml_excerpt_more($more)
{
    return '...';
}
add_filter('excerpt_more', 'ml_excerpt_more');

// Add custom classes to menu items
function ml_menu_classes($classes, $item, $args)
{
    if ($args->theme_location === 'primary') {
        $classes[] = 'flex items-center h-[48px] text-text hover:text-primary transition-colors';
    }
    if ($args->theme_location === 'footer') {
        $classes[] = 'block hover:text-white transition-colors';
    }

    return $classes;
}
add_filter('nav_menu_css_class', 'ml_menu_classes', 10, 3);

// Add Customizer options
function ml_customize_register($wp_customize)
{
    // Footer Section
    $wp_customize->add_section('footer_options', [
        'title' => __('Footer Options', 'ml'),
        'priority' => 120,
    ]);

    // About Text
    $wp_customize->add_setting('footer_about_text', [
        'default' => 'Marketing Lawyers is a specialized legal marketing agency helping law firms grow their practice.',
        'sanitize_callback' => 'sanitize_text_field',
    ]);
    $wp_customize->add_control('footer_about_text', [
        'label' => __('About Text', 'ml'),
        'section' => 'footer_options',
        'type' => 'textarea',
    ]);

    // Address
    $wp_customize->add_setting('footer_address', [
        'default' => '123 Legal Street<br>London, UK',
        'sanitize_callback' => 'wp_kses_post',
    ]);
    $wp_customize->add_control('footer_address', [
        'label' => __('Address', 'ml'),
        'section' => 'footer_options',
        'type' => 'textarea',
    ]);

    // Email
    $wp_customize->add_setting('footer_email', [
        'default' => '<EMAIL>',
        'sanitize_callback' => 'sanitize_email',
    ]);
    $wp_customize->add_control('footer_email', [
        'label' => __('Email', 'ml'),
        'section' => 'footer_options',
        'type' => 'text',
    ]);

    // Phone
    $wp_customize->add_setting('footer_phone', [
        'default' => '+44 ************',
        'sanitize_callback' => 'sanitize_text_field',
    ]);
    $wp_customize->add_control('footer_phone', [
        'label' => __('Phone', 'ml'),
        'section' => 'footer_options',
        'type' => 'text',
    ]);
}
add_action('customize_register', 'ml_customize_register');

// Disable comments completely
function ml_disable_comments()
{
    // Close comments on the front-end
    add_filter('comments_open', '__return_false', 20, 2);
    add_filter('pings_open', '__return_false', 20, 2);

    // Hide existing comments
    add_filter('comments_array', '__return_empty_array', 10, 2);

    // Remove comments page in menu
    add_action('admin_menu', function () {
        remove_menu_page('edit-comments.php');
    });

    // Remove comments links from admin bar
    add_action('init', function () {
        if (is_admin_bar_showing()) {
            remove_action('admin_bar_menu', 'wp_admin_bar_comments_menu', 60);
        }
    });

    // Disable comments dashboard widget
    add_action('wp_dashboard_setup', function () {
        remove_meta_box('dashboard_recent_comments', 'dashboard', 'normal');
    });

    // Redirect any user trying to access comments page
    add_action('admin_init', function () {
        global $pagenow;

        if ($pagenow === 'edit-comments.php') {
            wp_redirect(admin_url());
            exit;
        }
    });
}
add_action('after_setup_theme', 'ml_disable_comments');

/**
 * Add the Google API Key to ACF.
 */
add_filter('acf/fields/google_map/api', 'my_acf_google_map_api');
function my_acf_google_map_api($api)
{
    $api['key'] = get_field('google_maps_api_key', 'option');

    return $api;
}

/**
 * Include and register custom widgets.
 */
function ml_register_custom_widgets()
{
    // Include widget files
    require_once get_template_directory() . '/includes/widgets/class-ml-office-selector-widget.php';
    require_once get_template_directory() . '/includes/widgets/class-ml-footer-menu-widget.php';
    require_once get_template_directory() . '/includes/widgets/class-ml-contact-info-widget.php';
    require_once get_template_directory() . '/includes/widgets/class-ml-about-widget.php';
    require_once get_template_directory() . '/includes/widgets/class-ml-page-navigation-widget.php';
    require_once get_template_directory() . '/includes/widgets/class-ml-cta-widget.php';
    require_once get_template_directory() . '/includes/widgets/class-ml-copyright-widget.php';
    require_once get_template_directory() . '/includes/widgets/class-ml-legal-links-widget.php';
    require_once get_template_directory() . '/includes/widgets/class-ml-legal-aid-widget.php';

    // Register widgets
    register_widget('ML_Office_Selector_Widget');
    register_widget('ML_Footer_Menu_Widget');
    register_widget('ML_Contact_Info_Widget');
    register_widget('ML_About_Widget');
    register_widget('ML_Page_Navigation_Widget');
    register_widget('ML_CTA_Widget');
    register_widget('ML_Copyright_Widget');
    register_widget('ML_Legal_Links_Widget');
    register_widget('ML_Legal_Aid_Widget');
}
add_action('widgets_init', 'ml_register_custom_widgets');

// Helper to get icons
function get_svg_icon($name, $class = '')
{
    $file_path = get_template_directory() . "/assets/icons/{$name}.php";

    if (! file_exists($file_path)) {
        return "<!-- SVG icon not found: {$name} -->";
    }

    // Pass the class to the PHP file and include it
    ob_start();
    include $file_path;
    $svg = ob_get_clean();

    return $svg;
}


/**
 * Remove spaces in a string
 *
 */
function spaceless(String $string)
{
	$new_string = trim(str_replace(' ', '', $string));

	return $new_string;
}

/**
 * Check if the sidebar should be displayed
 *
 * @return bool Whether the sidebar should be displayed
 */
function ml_has_sidebar()
{
    return is_active_sidebar('sidebar-1');
}

/**
 * Include custom Gutenberg blocks
 */
function ml_include_custom_blocks()
{
    // Include FAQ blocks
    require_once get_template_directory() . '/includes/blocks/faq/register.php';
    require_once get_template_directory() . '/includes/blocks/faq/faq-list-register.php';
}
add_action('init', 'ml_include_custom_blocks', 5);

/**
 * Add rewrite rules for custom page template pagination
 */
function ml_add_custom_page_pagination_rewrite_rules()
{
    // Add rewrite rule for pages using custom templates with pagination
    // This handles URLs like /our-people/page/2/
    add_rewrite_rule(
        '^([^/]+)/page/([0-9]+)/?$',
        'index.php?pagename=$matches[1]&paged=$matches[2]',
        'top'
    );
}
add_action('init', 'ml_add_custom_page_pagination_rewrite_rules');

/**
 * Flush rewrite rules on theme activation
 */
function ml_flush_rewrite_rules_on_activation()
{
    ml_add_custom_page_pagination_rewrite_rules();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'ml_flush_rewrite_rules_on_activation');



/**
 * Custom Breadcrumbs
 */
function get_url_based_breadcrumbs() {
    if (is_front_page()) {
        return ''; // No breadcrumbs on homepage
    }

    $home_url = home_url();
    $current_url = home_url(add_query_arg([], $GLOBALS['wp']->request));
    $path = trim(parse_url($current_url, PHP_URL_PATH), '/');
    $segments = explode('/', $path);

    $breadcrumbs = '<nav class="breadcrumbs text-sm" aria-label="Breadcrumb">';
    $breadcrumbs .= '<a class="font-bold hover:underline hover:text-primary" href="' . esc_url($home_url) . '">Home</a>';

    $link = $home_url;

    foreach ($segments as $index => $segment) {
        $link .= '/' . $segment;

        // Skip numeric segments (like pagination or IDs)
        if (is_numeric($segment)) continue;

        // Attempt to prettify the label
        $label = ucwords(str_replace(['-', '_'], ' ', $segment));

        // Last segment is the current page
        if ($index === count($segments) - 1) {
            $breadcrumbs .= ' &raquo; <span>' . esc_html($label) . '</span>';
        } else {
            $breadcrumbs .= ' &raquo; <a class="font-bold hover:underline hover:text-primary" href="' . esc_url($link) . '">' . esc_html($label) . '</a>';
        }
    }

    $breadcrumbs .= '</nav>';

    return $breadcrumbs;
}

// CF7 adds <br> tags - lets remove them
add_filter('wpcf7_autop_or_not', '__return_false');

/**
 * Helper function to get coordinates for an address using Google Maps Geocoding API
 * This is used in the admin to help users find coordinates for their office locations
 */
function ml_get_coordinates_for_address($address) {
    // Get the Google Maps API key
    $api_key = get_field('google_maps_api_key', 'option');

    if (empty($api_key) || empty($address)) {
        return false;
    }

    // Prepare the address for the API request
    $address = urlencode($address);

    // Build the API URL
    $url = "https://maps.googleapis.com/maps/api/geocode/json?address={$address}&key={$api_key}";

    // Make the API request
    $response = wp_remote_get($url);

    // Check if the request was successful
    if (is_wp_error($response)) {
        return false;
    }

    // Parse the response
    $data = json_decode(wp_remote_retrieve_body($response), true);

    // Check if the geocoding was successful
    if ($data['status'] !== 'OK' || empty($data['results'][0]['geometry']['location'])) {
        return false;
    }

    // Return the coordinates
    return $data['results'][0]['geometry']['location'];
}

/**
 * Add a custom admin page to help users find coordinates for addresses
 */
function ml_add_geocoding_helper_page() {
    add_submenu_page(
        'options-general.php',
        'Geocoding Helper',
        'Geocoding Helper',
        'manage_options',
        'geocoding-helper',
        'ml_geocoding_helper_page_content'
    );
}
add_action('admin_menu', 'ml_add_geocoding_helper_page');

/**
 * Display the geocoding helper page content
 */
function ml_geocoding_helper_page_content() {
    ?>
    <div class="wrap">
        <h1>Geocoding Helper</h1>
        <p>Use this tool to find the latitude and longitude coordinates for an address. Enter an address below and click "Get Coordinates".</p>

        <div class="card" style="max-width: 600px; padding: 20px; margin-top: 20px;">
            <form method="post" action="">
                <?php wp_nonce_field('ml_geocoding_helper', 'ml_geocoding_nonce'); ?>
                <div style="margin-bottom: 15px;">
                    <label for="address" style="display: block; margin-bottom: 5px; font-weight: bold;">Address:</label>
                    <textarea name="address" id="address" rows="4" style="width: 100%;"><?php echo isset($_POST['address']) ? esc_textarea($_POST['address']) : ''; ?></textarea>
                </div>
                <div>
                    <input type="submit" name="ml_get_coordinates" class="button button-primary" value="Get Coordinates">
                </div>
            </form>

            <?php
            // Process the form submission
            if (isset($_POST['ml_get_coordinates']) && isset($_POST['ml_geocoding_nonce']) && wp_verify_nonce($_POST['ml_geocoding_nonce'], 'ml_geocoding_helper')) {
                $address = isset($_POST['address']) ? sanitize_textarea_field($_POST['address']) : '';

                if (!empty($address)) {
                    $coordinates = ml_get_coordinates_for_address($address);

                    if ($coordinates) {
                        ?>
                        <div style="margin-top: 20px; background-color: #f0f0f1; padding: 15px; border-left: 4px solid #2271b1;">
                            <h3 style="margin-top: 0;">Coordinates for: <?php echo esc_html($address); ?></h3>
                            <p><strong>Latitude:</strong> <?php echo esc_html($coordinates['lat']); ?></p>
                            <p><strong>Longitude:</strong> <?php echo esc_html($coordinates['lng']); ?></p>
                            <p><em>Copy these values into the Latitude and Longitude fields for your office location.</em></p>
                        </div>
                        <?php
                    } else {
                        ?>
                        <div style="margin-top: 20px; background-color: #f0f0f1; padding: 15px; border-left: 4px solid #d63638;">
                            <p><strong>Error:</strong> Could not find coordinates for the provided address. Please check the address and try again.</p>
                            <p>Make sure your Google Maps API key is valid and has the Geocoding API enabled.</p>
                        </div>
                        <?php
                    }
                }
            }
            ?>
        </div>

        <div class="card" style="max-width: 600px; padding: 20px; margin-top: 20px;">
            <h2>How to Use</h2>
            <ol>
                <li>Enter a complete address in the text area above.</li>
                <li>Click "Get Coordinates" to retrieve the latitude and longitude.</li>
                <li>Copy the latitude and longitude values to the corresponding fields in your office location settings.</li>
            </ol>
            <p><strong>Note:</strong> You need a valid Google Maps API key with the Geocoding API enabled for this tool to work.</p>
        </div>
    </div>
    <?php
}


// function add_csp_header() {
//     header(
//       "Content-Security-Policy: " .
//       "default-src 'self'; " .
//       "script-src 'self' 'unsafe-inline' https://maps.googleapis.com https://www.reviewsolicitors.co.uk; " .
//       "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://www.reviewsolicitors.co.uk; " .
//       "img-src 'self' data: https://*.googleapis.com https://*.gstatic.com https://www.reviewsolicitors.co.uk; " .
//       "font-src 'self' https://fonts.gstatic.com; " .
//       "connect-src 'self' https://maps.googleapis.com https://www.reviewsolicitors.co.uk; " .
//       "frame-src 'self' https://cdn.yoshki.com; " .
//       "object-src 'none'; " .
//       "upgrade-insecure-requests;"
//     );
//   }
//   add_action('send_headers', 'add_csp_header');

  /**
 * Excludes pages set to no-index in Yoast
 * 
 */
add_filter('get_pages', 'lwd_exclude_no_indexes_from_wp_list_pages', 10, 2);
function lwd_exclude_no_indexes_from_wp_list_pages($pages, $args)
{
	if (array_key_exists('walker', $args)) {
		foreach ($pages as $key => $item) {
			$_yoast_wpseo_meta_robots_noindex = get_post_meta($item->ID, '_yoast_wpseo_meta-robots-noindex', true);
			if ($_yoast_wpseo_meta_robots_noindex == 1) unset($pages[$key]);
		}
	}
	return $pages;
}