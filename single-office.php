<?php
/**
 * The template for displaying single 'office' custom post type entries.
 */
get_header();

?>

<div class="container mx-auto px-4 py-12">
    <?php while (have_posts()) {
        the_post();
        // Get office details from ACF fields
        $office_name = get_field('office_name') ? get_field('office_name') : get_the_title();
        $office_location = get_field('office_location');
        $dx_address = get_field('dx_address');
        $office_hours = get_field('office_hours');
        $office_additional = get_field('office_additional');
        ?>
        <article id="office-<?php the_ID(); ?>" <?php post_class('bg-white rounded-lg shadow-md overflow-hidden max-w-5xl mx-auto'); ?>>
            <!-- Office Header -->
            <div class="bg-primary text-white p-8">
                <h1 class="text-3xl md:text-4xl font-bold mb-2">
                    <?php echo esc_html($office_name); ?>
                </h1>

                <?php if (has_excerpt()) { ?>
                    <div class="text-xl opacity-90 mt-4">
                        <?php the_excerpt(); ?>
                    </div>
                <?php } ?>
            </div>

            <div class="md:flex">
                <!-- Office Details -->
                <div class="md:w-1/2 p-8">
                    <div class="mb-8">
                        <h2 class="text-2xl font-bold mb-4 text-gray-800">Contact Information</h2>

                        <?php if ($office_location) { ?>
                            <div class="flex items-start mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                <div>
                                    <h3 class="font-semibold text-gray-700 mb-1">Address</h3>
                                    <address class="not-italic">
                                        <?php
                                        // Extract postal code from address
                                        $address = $office_location['address'];
                                        $postal_code = '';

                                        // UK postal code regex pattern
                                        $uk_postcode_pattern = '/([A-Z]{1,2}[0-9][A-Z0-9]? ?[0-9][A-Z]{2}|[A-Z]{1,2}[0-9][A-Z0-9]?)/i';

                                        // Check if we can find a UK postal code
                                        if (preg_match($uk_postcode_pattern, $address, $matches)) {
                                            $postal_code = $matches[0];
                                            // Remove the postal code from the address for display
                                            $address_without_postcode = preg_replace($uk_postcode_pattern, '', $address);
                                            // Clean up any trailing commas or whitespace
                                            $address_without_postcode = rtrim($address_without_postcode, ", \t\n\r\0\x0B");
                                            echo esc_html($address_without_postcode);
                                        } else {
                                            echo esc_html($address);
                                        }
                                        ?>
                                        <?php if (!empty($postal_code)) : ?>
                                            <div class="font-bold mt-1"><?php echo esc_html($postal_code); ?></div>
                                        <?php endif; ?>
                                    </address>
                                </div>
                            </div>
                        <?php } ?>

                        <?php if ($dx_address) { ?>
                            <div class="flex items-start mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <div>
                                    <h3 class="font-semibold text-gray-700 mb-1">DX Address</h3>
                                    <p><?php echo esc_html($dx_address); ?></p>
                                </div>
                            </div>
                        <?php } ?>

                        <?php
                            // Display phone numbers if they exist
                            if (function_exists('get_field')) {
                                $phone = get_field('phone_number');
                                $fax = get_field('fax_number');

                                if ($phone) {
                                    ?>
                            <div class="flex items-start mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                <div>
                                    <h3 class="font-semibold text-gray-700 mb-1">Phone</h3>
                                    <p><a href="tel:<?php echo esc_attr(preg_replace('/[^0-9+]/', '', $phone)); ?>" class="text-primary hover:underline"><?php echo esc_html($phone); ?></a></p>
                                </div>
                            </div>
                        <?php
                                }
                                if ($fax) {
                                    ?>
                            <div class="flex items-start mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <div>
                                    <h3 class="font-semibold text-gray-700 mb-1">Fax</h3>
                                    <p><?php echo esc_html($fax); ?></p>
                                </div>
                            </div>
                        <?php
                                }
                            }
        ?>
                    </div>

                    <?php if ($office_hours && count($office_hours) > 0) { ?>
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold mb-4 text-gray-800">Office Hours</h2>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <table class="w-full">
                                    <tbody>
                                        <?php foreach ($office_hours as $hours) { ?>
                                            <tr class="border-b border-gray-200 last:border-0">
                                                <td class="py-3 font-medium capitalize"><?php echo esc_html($hours['opening_day']); ?></td>
                                                <td class="py-3 text-right">
                                                    <?php if ($hours['office_closed']) { ?>
                                                        <span class="text-red-600">Closed</span>
                                                    <?php } else { ?>
                                                        <?php
                                        $opening_hours = isset($hours['opening_hours']) ? $hours['opening_hours'] : '';
                                                        $closing_hours = isset($hours['closing_hours']) ? $hours['closing_hours'] : '';

                                                        if ($opening_hours && $closing_hours) {
                                                            echo esc_html($opening_hours . ' - ' . $closing_hours);
                                                        } else {
                                                            echo 'By appointment';
                                                        }
                                                        ?>
                                                    <?php } ?>
                                                </td>
                                            </tr>
                                        <?php } ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php } ?>

                    <?php
                    // Display social media links if they exist
                    if (function_exists('get_field') && have_rows('social_links')) {
                        ?>
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold mb-4 text-gray-800">Connect With Us</h2>
                            <div class="flex space-x-4">
                                <?php while (have_rows('social_links')) {
                                    the_row();
                                    $social_platform = get_sub_field('social_platform');
                                    $social_link = get_sub_field('social_link');

                                    if ($social_link) {
                                        $icon = '';
                                        $color = '';

                                        switch (strtolower($social_platform)) {
                                            case 'facebook':
                                                $icon = '<path d="M18 2h-3a5 5 0 00-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 011-1h3z"></path>';
                                                $color = 'hover:text-primary';
                                                break;
                                            case 'twitter':
                                            case 'x':
                                                $icon = '<path d="M23 3a10.9 10.9 0 01-3.14 1.53 4.48 4.48 0 00-7.86 3v1A10.66 10.66 0 013 4s-4 9 5 13a11.64 11.64 0 01-7 2c9 5 20 0 20-11.5a4.5 4.5 0 00-.08-.83A7.72 7.72 0 0023 3z"></path>';
                                                $color = 'hover:text-blue-400';
                                                break;
                                            case 'linkedin':
                                                $icon = '<path d="M16 8a6 6 0 016 6v7h-4v-7a2 2 0 00-2-2 2 2 0 00-2 2v7h-4v-7a6 6 0 016-6zM2 9h4v12H2z"></path><circle cx="4" cy="4" r="2"></circle>';
                                                $color = 'hover:text-blue-700';
                                                break;
                                            case 'instagram':
                                                $icon = '<rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1112.63 8 4 4 0 0116 11.37zm1.5-4.87h.01"></path>';
                                                $color = 'hover:text-pink-600';
                                                break;
                                            default:
                                                $icon = '<circle cx="12" cy="12" r="10"></circle>';
                                                $color = 'hover:text-primary';
                                        }
                                        ?>
                                    <a href="<?php echo esc_url($social_link); ?>" class="text-gray-500 <?php echo $color; ?>" target="_blank" rel="noopener noreferrer" aria-label="<?php echo esc_attr($social_platform); ?>">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                            <?php echo $icon; ?>
                                        </svg>
                                    </a>
                                <?php
                                    }
                                }
                        ?>
                            </div>
                        </div>
                    <?php } ?>
                </div>

                <!-- Map -->
                <div class="md:w-1/2">
                    <?php if ($office_location && ! empty($office_location['lat']) && ! empty($office_location['lng'])) { ?>
                        <div id="office-map" class="h-96 md:h-full min-h-[400px]"></div>
                    <?php } elseif (has_post_thumbnail()) { ?>
                        <div class="h-96 md:h-full min-h-[400px]">
                            <?php the_post_thumbnail('large', ['class' => 'w-full h-full object-cover']); ?>
                        </div>
                    <?php } ?>
                </div>
            </div>

            <?php if ($office_additional) { ?>
                <div class="p-8 prose max-w-none">
                    <h2 class="text-2xl font-bold mb-4 text-gray-800">Additional Information</h2>
                    <?php echo $office_additional; ?>
                </div>
            <?php } ?>

            <!-- Main content -->
            <?php if (get_the_content()) { ?>
                <div class="p-8 pt-0 prose max-w-none">
                    <?php the_content(); ?>
                </div>
            <?php } ?>


        </article>
    <?php } ?>
</div>

<?php if (have_posts() && get_field('office_location')) { ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const mapElement = document.getElementById('office-map');
        if (mapElement) {
            const officeLocation = <?php echo json_encode(get_field('office_location')); ?>;

            const map = new google.maps.Map(mapElement, {
                center: { lat: parseFloat(officeLocation.lat), lng: parseFloat(officeLocation.lng) },
                zoom: 15,
                styles: [
                    {
                        "featureType": "administrative",
                        "elementType": "all",
                        "stylers": [{"saturation": "-100"}]
                    },
                    {
                        "featureType": "administrative.province",
                        "elementType": "all",
                        "stylers": [{"visibility": "off"}]
                    },
                    {
                        "featureType": "landscape",
                        "elementType": "all",
                        "stylers": [{"saturation": -100}, {"lightness": 65}, {"visibility": "on"}]
                    },
                    {
                        "featureType": "poi",
                        "elementType": "all",
                        "stylers": [{"saturation": -100}, {"lightness": "50"}, {"visibility": "simplified"}]
                    },
                    {
                        "featureType": "road",
                        "elementType": "all",
                        "stylers": [{"saturation": "-100"}]
                    },
                    {
                        "featureType": "road.highway",
                        "elementType": "all",
                        "stylers": [{"visibility": "simplified"}]
                    },
                    {
                        "featureType": "road.arterial",
                        "elementType": "all",
                        "stylers": [{"lightness": "30"}]
                    },
                    {
                        "featureType": "road.local",
                        "elementType": "all",
                        "stylers": [{"lightness": "40"}]
                    },
                    {
                        "featureType": "transit",
                        "elementType": "all",
                        "stylers": [{"saturation": -100}, {"visibility": "simplified"}]
                    },
                    {
                        "featureType": "water",
                        "elementType": "geometry",
                        "stylers": [{"hue": "#ffff00"}, {"lightness": -25}, {"saturation": -97}]
                    },
                    {
                        "featureType": "water",
                        "elementType": "labels",
                        "stylers": [{"lightness": -25}, {"saturation": -100}]
                    }
                ]
            });

            const marker = new google.maps.Marker({
                position: { lat: parseFloat(officeLocation.lat), lng: parseFloat(officeLocation.lng) },
                map: map,
                title: '<?php echo esc_js($office_name); ?>'
            });

            <?php
            // Extract postal code from address
            $address = $office_location['address'];
            $postal_code = '';

            // UK postal code regex pattern
            $uk_postcode_pattern = '/([A-Z]{1,2}[0-9][A-Z0-9]? ?[0-9][A-Z]{2}|[A-Z]{1,2}[0-9][A-Z0-9]?)/i';

            // Check if we can find a UK postal code
            if (preg_match($uk_postcode_pattern, $address, $matches)) {
                $postal_code = $matches[0];
                // Remove the postal code from the address for display
                $address_without_postcode = preg_replace($uk_postcode_pattern, '', $address);
                // Clean up any trailing commas or whitespace
                $address_without_postcode = rtrim($address_without_postcode, ", \t\n\r\0\x0B");
            } else {
                $address_without_postcode = $address;
            }
            ?>

            const infoWindow = new google.maps.InfoWindow({
                content: '<div class="p-2"><strong><?php echo esc_js($office_name); ?></strong><br><?php echo esc_js($address_without_postcode); ?><?php if (!empty($postal_code)) : ?><br><strong><?php echo esc_js($postal_code); ?></strong><?php endif; ?></div>'
            });

            marker.addListener('click', function() {
                infoWindow.open(map, marker);
            });
        }
    });
</script>
<?php } ?>

<?php get_footer(); ?>