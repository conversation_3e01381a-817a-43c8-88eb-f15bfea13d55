@tailwind base;
@tailwind components;
@tailwind utilities;

/* WordPress content area styles */
@layer components {
    .content {
      /* Headings */
      h1 {
        @apply scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl;
      }

      h2 {
        @apply mt-10 scroll-m-20 pb-2 text-3xl font-semibold tracking-tight transition-colors first:mt-0;
      }

      h3 {
        @apply mt-8 scroll-m-20 text-2xl font-semibold tracking-tight;
      }

      /* Paragraphs */
      p {
        @apply leading-7 [&:not(:first-child)]:mt-6;
      }

      /* Links */
      a {
        @apply font-medium text-primary underline underline-offset-4 text-secondary;
      }

      /* Blockquotes */
      blockquote {
        @apply mt-6 border-l-2 pl-6 italic;
      }

      /* Lists */
      ul {
        @apply my-6 ml-6 list-disc [&>li]:mt-2;
      }

      /* Tables */
      table {
        @apply w-full;
      }

      tr {
        @apply m-0 border-t p-0;
      }

      tr:nth-child(even) {
        @apply bg-gray-100;
      }

      th {
        @apply border px-4 py-2 text-left font-bold [&[align=center]]:text-center [&[align=right]]:text-right;
      }

      td {
        @apply border px-4 py-2 text-left [&[align=center]]:text-center [&[align=right]]:text-right;
      }

      thead {
        @apply text-white bg-secondary border-b-0;
      }

      /* Table wrapper */
      .wp-block-table,
      .table-container {
        @apply my-6 w-full overflow-y-auto;
      }
    }
  }

  body {
    @apply text-text;
  }

/* Sidebar Widget Styles */
.widget {
    @apply mb-8 md:mb-0;
}

footer a {
    @apply transition ease-in-out hover:text-primary hover:underline;
}

.widget:last-child {
    @apply mb-0;
}

.widget-title {
    @apply text-lg font-bold mb-4 text-secondary;
}

/* Page Navigation Widget */
.ml-page-navigation-widget ul {
    @apply list-none p-0 m-0;
}

.ml-page-navigation-widget li {
    @apply border-b border-gray-100 py-2;
}

.ml-page-navigation-widget li:last-child {
    @apply border-b-0;
}

.ml-page-navigation-widget a {
    @apply block text-gray-700 hover:text-primary transition-colors;
}

/* CTA Widget */
.ml-cta-widget .cta-box {
    @apply bg-tertiary p-6 rounded-lg border-l-4 border-primary;
}

/* Footer Smallprint Widget */
.footer-smallprint {
    @apply flex flex-wrap max-w-[985px] items-center;
}

.footer-smallprint .widget {
    @apply mx-2 my-1;
}

.footer-smallprint .widget p {
    @apply text-text text-sm text-left;
}



.ml-legal-links-widget .legal-links a {
    @apply text-gray-400 hover:text-gray-300 transition-colors;
}


/* Accessibility styles */
#main-content:focus {
    @apply outline-none;
}

/* Legal Aid Widget */


/* Custom styles below this line */

/* FAQ Block Styles */
.ml-faq-list {
    @apply my-6;
}

.ml-faq-item {
    @apply mb-4 rounded overflow-hidden;
}

.ml-faq-item summary {
    @apply flex justify-between items-center p-4 border-tertiary cursor-pointer hover:border-slate-200 transition-colors;
}

.ml-faq-item summary::-webkit-details-marker {
    @apply hidden;
}

.ml-faq-question {
    @apply font-bold;
}

.ml-faq-icon {
    @apply w-6 h-6 text-secondary transform transition-transform;
}

.ml-faq-item[open] .ml-faq-icon {
    @apply rotate-180;
}

.ml-faq-answer {
    @apply p-4 bg-white;
}

/* Side Panel Styles */
#side-panel {
    @apply shadow-xl;
    height: 100vh;
    height: 100dvh; /* Use dynamic viewport height for mobile browsers */
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Side panel inner container */
#side-panel .h-full {
    @apply flex flex-col;
    min-height: 100%;
    height: auto; /* Allow content to determine height */
    overflow-y: visible; /* Let the parent handle scrolling */
}

/* Ensure proper contrast for WCAG AA compliance */
#side-panel nav a {
    @apply text-gray-800 font-medium;
}

#side-panel nav a:hover,
#side-panel nav a:focus {
    @apply text-primary;
}

/* Focus styles for accessibility */
a:focus, button:focus {
    @apply outline-none ring-2 ring-primary ring-offset-2;
}

/* Prevent scrolling when side panel is open */
body.overflow-hidden {
    overflow: hidden !important;
}

/* Header scroll behavior */
.header-logo-section {
    transition: 0.3s ease-in-out;
}

.header-logo-section.scrolled {
    @apply md:h-[115px];
}

/* Custom logo styling */
.custom-logo-link img {
    @apply transition-all duration-300 ease-in-out md:w-[300px];
}

.header-logo-section.scrolled .custom-logo-link img {
    @apply md:w-[245px];
}



/* Submenu styling for side panel */
#side-panel .sub-menu {
    @apply space-y-2;
}

/* Menu item container styling */
.menu-item-container {
    @apply py-1 w-full;
}

/* Submenu panel styling */
.submenu-panel {
    @apply overflow-y-auto;
    height: 100vh;
    height: 100dvh; /* Use dynamic viewport height for mobile browsers */
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    overscroll-behavior: contain; /* Prevent scroll chaining */
}

/* Back button styling */
.back-button {
    @apply text-left hover:bg-gray-50 hover:text-primary transition-colors;
}

/* Submenu toggle button styling */
.submenu-toggle {
    @apply flex items-center justify-center;
}

/* Current menu item styling */
#side-panel .current-menu-item > a > span,
#side-panel .current-menu-parent > a > span {
    @apply text-primary font-bold;
}

/* Hamburger button styling */
#open-side-panel {
    @apply inline-flex items-center justify-center;
}

/* Close button styling */
#close-side-panel {
    @apply inline-flex items-center justify-center;
}

/* Overlay transition */
#side-panel-overlay {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}
#side-panel-overlay.opacity-100 {
    opacity: 1;
}

/* Contact Form 7 Styles */

/* Form Container */
.wpcf7-form {
    @apply bg-white rounded-lg p-6 max-w-2xl mx-auto;
}

/* Form Title */
.wpcf7-form .title {
    @apply text-2xl font-bold mb-4 uppercase;
}

/* Form Intro Text */
.wpcf7-form .intro {
    @apply text-gray-600 mb-6;
}

/* Field Row */
.wpcf7-form .field-row {
    @apply mb-6;
}

.wpcf7-form .field-row.consent {
    @apply p-0;
}

/* Labels */
.wpcf7-form label {
    @apply block mb-2 font-bold;
}

/* Text Inputs, Email, Tel */
.wpcf7-form input[type="text"],
.wpcf7-form input[type="email"],
.wpcf7-form input[type="tel"],
.wpcf7-form textarea {
    @apply w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-primary transition-colors;
}

/* Textarea */
.wpcf7-form textarea {
    @apply h-32 resize-y min-h-24;
}

/* Fieldset */
.wpcf7-form fieldset {
    @apply border border-gray-200 rounded-md p-4 mb-6;
}

/* Legend */
.wpcf7-form legend {
    @apply px-2 font-medium text-gray-700;
}

/* Acceptance Checkbox */
.wpcf7-form .wpcf7-acceptance {
    @apply block mt-2;
}

.wpcf7-form .wpcf7-acceptance label {
    @apply flex items-start font-bold;
}

.wpcf7-form .wpcf7-acceptance input[type="checkbox"] {
    @apply mr-2 h-8 w-8 text-primary border-gray-300 rounded focus:ring-primary;
}

/* Submit Button */
.wpcf7-form .wpcf7-submit {
    @apply bg-primary hover:bg-opacity-90 text-white font-medium py-3 px-6 rounded-md transition-colors w-full md:w-full cursor-pointer;
}

.wpcf7-form .wpcf7-submit:disabled {
    @apply bg-gray-300 text-gray-500 cursor-not-allowed;
}

/* GDPR Text */
.wpcf7-form .gdpr {
    @apply text-sm;
}

/* Links in Form */
.wpcf7-form a {
    @apply text-primary hover:text-opacity-80 underline;
}

/* Response Messages */
.wpcf7-response-output {
    @apply mt-6 p-4 rounded-md border text-sm;
}

/* Success Message */
.wpcf7-form.sent .wpcf7-response-output {
    @apply bg-green-50 border-green-500 text-green-700;
}

/* Error Message */
.wpcf7-form.invalid .wpcf7-response-output,
.wpcf7-form.failed .wpcf7-response-output,
.wpcf7-form.aborted .wpcf7-response-output {
    @apply bg-red-50 border-red-500 text-red-700;
}

/* Validation Message */
.wpcf7-form.unaccepted .wpcf7-response-output,
.wpcf7-form.spam .wpcf7-response-output {
    @apply bg-yellow-50 border-yellow-500 text-yellow-700;
}

/* Loading Indicator */
.wpcf7-form.submitting .wpcf7-spinner {
    @apply opacity-100 inline-block ml-2 align-middle;
}

/* Field-specific validation errors */
.wpcf7-not-valid-tip {
    @apply text-red-600 text-sm mt-1 block;
}

.wpcf7-form input.wpcf7-not-valid,
.wpcf7-form textarea.wpcf7-not-valid,
.wpcf7-form select.wpcf7-not-valid {
    @apply border-red-500 bg-red-50;
}

/* Sidebar Form Specific Styles */
.sidebar .wpcf7-form {
    @apply bg-tertiary p-4;
}

/* .sidebar .wpcf7-form .title {
    @apply text-2xl mb-3 uppercase;
} */

/*
.sidebar .wpcf7-form .field-row {
    @apply mb-4;
}

.sidebar .wpcf7-form input[type="text"],
.sidebar .wpcf7-form input[type="email"],
.sidebar .wpcf7-form input[type="tel"],
.sidebar .wpcf7-form textarea {
    @apply py-2 px-3;
} */

.home  .wpcf7-form title{
    @apply text-center;
}

/* Style the ul so it has checks instead of bullets */
#services ul {
    @apply list-none pl-0;
}

#services ul li {
    @apply flex items-center my-0;
}

#services ul li::before {
    content: '';
    display: inline-block;
    width: 2em;
    height: 2em;
    margin-right: 0.5em;
    background: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23831427"%3E%3Cpath d="M9 16.2l-4.2-4.2-1.4 1.4L9 19 21 7l-1.4-1.4z"/%3E%3C/svg%3E') no-repeat center;
    background-size: contain;
    color: #831427;
}

.pagination .page-numbers {
    @apply flex flex-wrap gap-2;
}

.pagination a {
    @apply bg-tertiary rounded-lg min-w-8 h-8 px-2 flex items-center justify-center hover:text-opacity-80 underline;
}

.pagination span {
    @apply text-primary bg-tertiary rounded-lg min-w-8 h-8 px-2 flex items-center justify-center;
}

#menu-top-bar-menu a {
    @apply hover:text-primary hover:underline;
}