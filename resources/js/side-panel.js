/**
 * Side panel functionality
 * Handles opening and closing the side panel, focus management, and keyboard navigation
 */
document.addEventListener('DOMContentLoaded', function() {
    initSidePanel();
});

/**
 * Initialize the side panel functionality
 */
function initSidePanel() {
    const openButton = document.getElementById('open-side-panel');
    const closeButton = document.getElementById('close-side-panel');
    const sidePanel = document.getElementById('side-panel');
    const overlay = document.getElementById('side-panel-overlay');
    const body = document.body;

    // If any of these elements don't exist, exit early
    if (!openButton || !closeButton || !sidePanel || !overlay) {
        console.error('Side panel elements not found');
        return;
    }

    // Ensure the side panel has the inert attribute when closed
    if (!sidePanel.hasAttribute('inert')) {
        sidePanel.setAttribute('inert', '');
    }

    // Store the element that had focus before the side panel was opened
    let lastFocusedElement;

    // All focusable elements in the side panel
    let focusableElements;

    // Open the side panel
    function openSidePanel() {
        // Store the current focus
        lastFocusedElement = document.activeElement;

        // Show the panel and overlay
        sidePanel.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
        overlay.classList.add('opacity-100');

        // Update ARIA attributes
        openButton.setAttribute('aria-expanded', 'true');
        sidePanel.setAttribute('aria-hidden', 'false');

        // Remove inert attribute to make the panel interactive
        if (sidePanel.hasAttribute('inert')) {
            sidePanel.removeAttribute('inert');
        }

        // Prevent scrolling on the body
        body.classList.add('overflow-hidden');

        // Enable tabbing for main panel elements
        const mainPanelElements = sidePanel.querySelectorAll('.menu-item-container a, .menu-item > a, .submenu-toggle, #close-side-panel');
        mainPanelElements.forEach(el => {
            el.setAttribute('tabindex', '0');
        });

        // Disable tabbing for all submenu panels
        const submenuPanels = sidePanel.querySelectorAll('.submenu-panel');
        submenuPanels.forEach(panel => {
            panel.setAttribute('aria-hidden', 'true');
            const panelElements = panel.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            panelElements.forEach(el => {
                el.setAttribute('tabindex', '-1');
            });
        });

        // Get all focusable elements in the main panel
        focusableElements = sidePanel.querySelectorAll('button:not([tabindex="-1"]), [href]:not([tabindex="-1"]), input:not([tabindex="-1"]), select:not([tabindex="-1"]), textarea:not([tabindex="-1"]), [tabindex]:not([tabindex="-1"])');

        // Focus the first element (close button)
        setTimeout(() => {
            closeButton.focus();
        }, 100);

        // Add event listeners
        document.addEventListener('keydown', handleKeyDown);

        // Setup focus trap for the main panel
        if (typeof window.setupFocusTrap === 'function') {
            window.setupFocusTrap(sidePanel);
        }
    }

    // Close the side panel
    function closeSidePanel() {
        // Hide the panel and overlay
        sidePanel.classList.add('translate-x-full');
        overlay.classList.add('hidden');
        overlay.classList.remove('opacity-100');

        // Update ARIA attributes
        openButton.setAttribute('aria-expanded', 'false');
        sidePanel.setAttribute('aria-hidden', 'true');

        // Add inert attribute to make the panel non-interactive when closed
        sidePanel.setAttribute('inert', '');

        // Allow scrolling on the body again
        body.classList.remove('overflow-hidden');

        // Reset all submenu panels to their initial state
        const submenuPanels = sidePanel.querySelectorAll('.submenu-panel');
        submenuPanels.forEach(panel => {
            panel.classList.add('translate-x-full');
        });

        // Restore focus to the element that had it before the panel was opened
        if (lastFocusedElement) {
            lastFocusedElement.focus();
        }

        // Remove event listeners
        document.removeEventListener('keydown', handleKeyDown);
    }

    // Handle keyboard events
    function handleKeyDown(e) {
        // Close on ESC key
        if (e.key === 'Escape') {
            closeSidePanel();
            return;
        }

        // Handle Tab key for focus trap
        if (e.key === 'Tab') {
            // If there are no focusable elements, do nothing
            if (focusableElements.length === 0) return;

            // Get the currently visible panel
            const visiblePanel = Array.from(sidePanel.querySelectorAll('.submenu-panel'))
                .find(panel => !panel.classList.contains('translate-x-full'));

            // If a submenu panel is visible, let its own focus trap handle tabbing
            if (visiblePanel) return;

            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];

            // If shift+tab on first element, move to last element
            if (e.shiftKey && document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            }
            // If tab on last element, move to first element
            else if (!e.shiftKey && document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }

    // Add event listeners
    openButton.addEventListener('click', openSidePanel);
    closeButton.addEventListener('click', closeSidePanel);
    overlay.addEventListener('click', closeSidePanel);
}
