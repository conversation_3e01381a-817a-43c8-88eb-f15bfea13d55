// Side Panel Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize side panel when DOM is fully loaded
    initSidePanel();
});

// Use the global utility functions from utils.js
const { setFocusableElementsTabIndex, setupFocusTrap } = window;

/**
 * Initialize the side panel functionality
 */
function initSidePanel() {
    const openButton = document.getElementById('open-side-panel');
    const closeButton = document.getElementById('close-side-panel');
    const sidePanel = document.getElementById('side-panel');
    const overlay = document.getElementById('side-panel-overlay');

    // If any of these elements don't exist, exit early
    if (!openButton || !closeButton || !sidePanel || !overlay) {
        console.error('Side panel elements not found');
        return;
    }

    // Add submenu toggle functionality for accessibility
    setupSubmenuToggles();
}

/**
 * Setup submenu toggles for the side panel navigation
 */
function setupSubmenuToggles() {
    // Find all menu items with submenus in the side panel
    const menuItemsWithChildren = document.querySelectorAll('#side-panel .menu-item-has-children');

    // Create a back button template
    const createBackButton = (text) => {
        const backBtn = document.createElement('button');
        backBtn.className = 'back-button rounded flex items-center text-gray-800 py-3 px-4 w-full text-lg font-medium';
        backBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <span>Back to ${text}</span>
        `;
        return backBtn;
    };

    menuItemsWithChildren.forEach(item => {
        const link = item.querySelector('a');
        const submenu = item.querySelector('.sub-menu');
        const linkText = link.textContent.trim();

        if (link && submenu) {
            // Create a container for the menu item to allow for flex positioning
            const menuItemContainer = document.createElement('div');
            menuItemContainer.className = 'menu-item-container flex justify-between items-center w-full';

            // Wrap the existing link in the container
            link.parentNode.insertBefore(menuItemContainer, link);
            menuItemContainer.appendChild(link);

            // Create a toggle button
            const toggleBtn = document.createElement('button');
            toggleBtn.className = 'submenu-toggle ml-auto text-primary focus:outline-none focus:ring-2 focus:ring-primary rounded p-2';
            toggleBtn.setAttribute('aria-expanded', 'false');
            toggleBtn.setAttribute('aria-label', `Open ${linkText} submenu`);
            toggleBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>';

            // Add the toggle button to the container
            menuItemContainer.appendChild(toggleBtn);

            // Create a submenu panel
            const submenuPanel = document.createElement('div');
            submenuPanel.className = 'submenu-panel fixed top-0 right-0 w-full h-full bg-white z-50 transform translate-x-full transition-transform duration-300 ease-in-out p-4 md:p-12 lg:p-14';
            submenuPanel.style.height = '100%';
            submenuPanel.setAttribute('aria-hidden', 'true');

            // Create a header for the submenu panel
            const submenuHeader = document.createElement('div');
            submenuHeader.className = 'flex items-center justify-between p-4 border-b';

            // Add a back button to the submenu panel
            const backBtn = createBackButton('Menu');
            submenuHeader.appendChild(backBtn);

            // Create a container for the submenu content
            const submenuContent = document.createElement('div');
            submenuContent.className = 'p-4';

            // Move the submenu into the submenu content
            submenuContent.appendChild(submenu);

            // Assemble the submenu panel
            submenuPanel.appendChild(submenuHeader);
            submenuPanel.appendChild(submenuContent);

            // Add the submenu panel to the side panel
            document.querySelector('#side-panel .h-full').appendChild(submenuPanel);

            // Set initial tabindex for all focusable elements in the panel
            setFocusableElementsTabIndex(submenuPanel, '-1');

            // Setup nested submenus if any
            const nestedMenuItems = submenu.querySelectorAll('.menu-item-has-children');
            if (nestedMenuItems.length > 0) {
                setupNestedSubmenus(nestedMenuItems, submenuPanel, linkText);
            }

            // Toggle submenu panel on button click
            toggleBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Show the submenu panel
                submenuPanel.classList.remove('translate-x-full');
                submenuPanel.setAttribute('aria-hidden', 'false');
                toggleBtn.setAttribute('aria-expanded', 'true');

                // Enable tabbing in the submenu panel
                setFocusableElementsTabIndex(submenuPanel, '0');

                // Setup focus trap for the submenu panel
                setupFocusTrap(submenuPanel);

                // Focus the back button
                setTimeout(() => {
                    backBtn.focus();
                }, 100);
            });

            // Handle back button click
            backBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Hide the submenu panel
                submenuPanel.classList.add('translate-x-full');
                submenuPanel.setAttribute('aria-hidden', 'true');
                toggleBtn.setAttribute('aria-expanded', 'false');

                // Disable tabbing in the submenu panel
                setFocusableElementsTabIndex(submenuPanel, '-1');

                // Return focus to the toggle button
                setTimeout(() => {
                    toggleBtn.focus();
                }, 100);
            });
        }
    });
}

/**
 * Setup nested submenus within a submenu panel
 */
function setupNestedSubmenus(nestedMenuItems, parentPanel, parentText) {
    // Create a back button template
    const createBackButton = (text) => {
        const backBtn = document.createElement('button');
        backBtn.className = 'back-button flex items-center text-gray-800 py-3 px-4 w-full text-lg font-medium';
        backBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <span>Back to ${text}</span>
        `;
        return backBtn;
    };

    nestedMenuItems.forEach(item => {
        const link = item.querySelector('a');
        const submenu = item.querySelector('.sub-menu');
        const linkText = link.textContent.trim();

        if (link && submenu) {
            // Create a container for the menu item to allow for flex positioning
            const menuItemContainer = document.createElement('div');
            menuItemContainer.className = 'menu-item-container flex justify-between items-center w-full';

            // Wrap the existing link in the container
            link.parentNode.insertBefore(menuItemContainer, link);
            menuItemContainer.appendChild(link);

            // Create a toggle button
            const toggleBtn = document.createElement('button');
            toggleBtn.className = 'submenu-toggle ml-auto text-primary focus:outline-none focus:ring-2 focus:ring-primary rounded p-2';
            toggleBtn.setAttribute('aria-expanded', 'false');
            toggleBtn.setAttribute('aria-label', `Open ${linkText} submenu`);
            toggleBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>';

            // Add the toggle button to the container
            menuItemContainer.appendChild(toggleBtn);

            // Create a nested submenu panel
            const nestedPanel = document.createElement('div');
            nestedPanel.className = 'submenu-panel fixed top-0 right-0 w-full h-full bg-white z-50 transform translate-x-full transition-transform duration-300 ease-in-out p-4 md:p-12 lg:p-14';
            nestedPanel.style.height = '100%';
            nestedPanel.setAttribute('aria-hidden', 'true');

            // Create a header for the nested submenu panel
            const nestedHeader = document.createElement('div');
            nestedHeader.className = 'flex items-center justify-between p-4';

            // Add a back button to the nested submenu panel
            const backBtn = createBackButton(parentText);
            nestedHeader.appendChild(backBtn);

            // Create a container for the nested submenu content
            const nestedContent = document.createElement('div');
            nestedContent.className = 'p-4';

            // Move the submenu into the nested content
            nestedContent.appendChild(submenu);

            // Assemble the nested submenu panel
            nestedPanel.appendChild(nestedHeader);
            nestedPanel.appendChild(nestedContent);

            // Add the nested submenu panel to the side panel
            document.querySelector('#side-panel .h-full').appendChild(nestedPanel);

            // Set initial tabindex for all focusable elements in the panel
            setFocusableElementsTabIndex(nestedPanel, '-1');

            // Toggle nested submenu panel on button click
            toggleBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Show the nested submenu panel
                nestedPanel.classList.remove('translate-x-full');
                nestedPanel.setAttribute('aria-hidden', 'false');
                toggleBtn.setAttribute('aria-expanded', 'true');

                // Enable tabbing in the nested submenu panel
                setFocusableElementsTabIndex(nestedPanel, '0');

                // Setup focus trap for the nested submenu panel
                setupFocusTrap(nestedPanel);

                // Hide the parent panel
                parentPanel.classList.add('translate-x-full');
                parentPanel.setAttribute('aria-hidden', 'true');

                // Disable tabbing in the parent panel
                setFocusableElementsTabIndex(parentPanel, '-1');

                // Focus the back button
                setTimeout(() => {
                    backBtn.focus();
                }, 100);
            });

            // Handle back button click
            backBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Hide the nested submenu panel
                nestedPanel.classList.add('translate-x-full');
                nestedPanel.setAttribute('aria-hidden', 'true');
                toggleBtn.setAttribute('aria-expanded', 'false');

                // Disable tabbing in the nested submenu panel
                setFocusableElementsTabIndex(nestedPanel, '-1');

                // Show the parent panel
                parentPanel.classList.remove('translate-x-full');
                parentPanel.setAttribute('aria-hidden', 'false');

                // Enable tabbing in the parent panel
                setFocusableElementsTabIndex(parentPanel, '0');

                // Return focus to the toggle button
                setTimeout(() => {
                    toggleBtn.focus();
                }, 100);
            });
        }
    });
}