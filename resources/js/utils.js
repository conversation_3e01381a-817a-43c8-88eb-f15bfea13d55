/**
 * Utility functions for the theme
 */

/**
 * Set tabindex for all focusable elements in a panel
 * @param {HTMLElement} panel - The panel containing focusable elements
 * @param {string} tabIndex - The tabindex value to set
 */
window.setFocusableElementsTabIndex = function(panel, tabIndex) {
    const focusableElements = panel.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
    focusableElements.forEach(el => {
        el.setAttribute('tabindex', tabIndex);
    });
};

/**
 * Setup a focus trap for a panel to keep focus within it
 * @param {HTMLElement} panel - The panel to trap focus within
 */
window.setupFocusTrap = function(panel) {
    // Remove any existing event listeners first
    panel.removeEventListener('keydown', handlePanelKeyDown);
    
    // Add the event listener
    panel.addEventListener('keydown', handlePanelKeyDown);
    
    function handlePanelKeyDown(e) {
        // Only handle Tab key
        if (e.key !== 'Tab') return;
        
        // Get all focusable elements in the panel
        const focusableElements = panel.querySelectorAll('button:not([tabindex="-1"]), [href]:not([tabindex="-1"]), input:not([tabindex="-1"]), select:not([tabindex="-1"]), textarea:not([tabindex="-1"]), [tabindex]:not([tabindex="-1"])');
        
        if (focusableElements.length === 0) return;
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        // If shift+tab on first element, move to last element
        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        }
        // If tab on last element, move to first element
        else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }
};
