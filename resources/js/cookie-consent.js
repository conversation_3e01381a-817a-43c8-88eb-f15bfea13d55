document.addEventListener('DOMContentLoaded', function() {
    const cookieConsent = document.getElementById('cookieConsent');
    const acceptCookies = document.getElementById('acceptCookies');
    const rejectCookies = document.getElementById('rejectCookies');
    const configureCookies = document.getElementById('configureCookies');
    const cookieModal = document.getElementById('cookiePreferencesModal');
    const savePreferences = document.getElementById('savePreferences');
    const cancelPreferences = document.getElementById('cancelPreferences');
    const body = document.body;

    // Get the main content wrapper to make inert when modal is open
    const mainContent = document.getElementById('main-content');
    const header = document.querySelector('header');
    const siteHeader = document.querySelector('.bg-white.w-full.h-\\[170px\\]');

    // Variables to store scroll position and last focused element
    let scrollPosition = 0;
    let lastFocusedElement = null;

    // Check if consent was already given
    const consent = getCookieConsent();

    if (!consent) {
      // Show banner if no consent exists
      cookieConsent.classList.remove('hidden');

      // If the cookie modal is open, make the page inert
      if (!cookieModal.classList.contains('hidden')) {
        makePageInert(true);
      }
    }

    // Accept all cookies
    acceptCookies.addEventListener('click', function() {
      setCookieConsent({
        essential: true,
        analytics: true,
        marketing: true
      });
      cookieConsent.classList.add('hidden');
      loadCookiesBasedOnConsent();
    });

    // Reject non-essential
    rejectCookies.addEventListener('click', function() {
      setCookieConsent({
        essential: true,
        analytics: false,
        marketing: false
      });
      cookieConsent.classList.add('hidden');
      loadCookiesBasedOnConsent();
    });

    // Configure cookies
    configureCookies.addEventListener('click', function() {
      // Store the element that had focus before opening the modal
      lastFocusedElement = document.activeElement;

      cookieModal.classList.remove('hidden');

      // Make the rest of the page inert
      makePageInert(true);

      // Add keyboard event listener to close modal on Escape key
      document.addEventListener('keydown', handleEscapeKey);

      // Add keyboard event listener for focus trap
      document.addEventListener('keydown', trapFocus);

      // Set focus to the first focusable element in the modal
      setTimeout(() => {
        const firstFocusableElement = cookieModal.querySelector('button, [href], input:not([disabled]), select, textarea, [tabindex]:not([tabindex="-1"])');
        if (firstFocusableElement) {
          firstFocusableElement.focus();
        }
      }, 100);
    });

    // Handle Escape key press
    function handleEscapeKey(event) {
      if (event.key === 'Escape' && !cookieModal.classList.contains('hidden')) {
        cookieModal.classList.add('hidden');
        makePageInert(false);
        document.removeEventListener('keydown', handleEscapeKey);
        document.removeEventListener('keydown', trapFocus);

        // Restore focus to the element that had it before the modal was opened
        if (lastFocusedElement) {
          lastFocusedElement.focus();
        }
      }
    }

    // Trap focus within the modal
    function trapFocus(event) {
      // Only trap focus if the modal is visible
      if (cookieModal.classList.contains('hidden')) return;

      // Only handle Tab key
      if (event.key !== 'Tab') return;

      // Get all focusable elements in the modal
      const focusableElements = cookieModal.querySelectorAll(
        'button, [href], input:not([disabled]), select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      if (focusableElements.length === 0) return;

      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];

      // If shift+tab on first element, move to last element
      if (event.shiftKey && document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
      // If tab on last element, move to first element
      else if (!event.shiftKey && document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }

    // Save preferences
    savePreferences.addEventListener('click', function() {
      const analytics = document.getElementById('analyticsCookies').checked;
      const marketing = document.getElementById('marketingCookies').checked;

      setCookieConsent({
        essential: true,
        analytics: analytics,
        marketing: marketing
      });

      cookieModal.classList.add('hidden');
      cookieConsent.classList.add('hidden');

      // Remove inert attribute from the page
      makePageInert(false);

      // Remove keyboard event listeners
      document.removeEventListener('keydown', handleEscapeKey);
      document.removeEventListener('keydown', trapFocus);

      // Restore focus to the element that had it before the modal was opened
      if (lastFocusedElement) {
        lastFocusedElement.focus();
      }

      loadCookiesBasedOnConsent();
    });

    // Cancel preferences
    cancelPreferences.addEventListener('click', function() {
      cookieModal.classList.add('hidden');

      // Remove inert attribute from the page
      makePageInert(false);

      // Remove keyboard event listeners
      document.removeEventListener('keydown', handleEscapeKey);
      document.removeEventListener('keydown', trapFocus);

      // Restore focus to the element that had it before the modal was opened
      if (lastFocusedElement) {
        lastFocusedElement.focus();
      }
    });

    // Helper functions
    function setCookieConsent(consent) {
      const expiryDate = new Date();
      expiryDate.setFullYear(expiryDate.getFullYear() + 1);

      document.cookie = `cookieConsent=${JSON.stringify(consent)}; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax`;
    }

    function getCookieConsent() {
      const cookieString = document.cookie.split('; ').find(row => row.startsWith('cookieConsent='));
      if (!cookieString) return null;

      try {
        return JSON.parse(cookieString.split('=')[1]);
      } catch {
        return null;
      }
    }

    function loadCookiesBasedOnConsent() {
      const consent = getCookieConsent();
      if (!consent) return;

      // Load analytics scripts if consented
      if (consent.analytics) {
        // Example: Load Google Analytics
        // window.dataLayer = window.dataLayer || [];
        // function gtag(){dataLayer.push(arguments);}
        // gtag('js', new Date());
        // gtag('config', 'YOUR_GA_ID');
      }

      // Load marketing scripts if consented
      if (consent.marketing) {
        // Example: Load Facebook Pixel
        // !function(f,b,e,v,n,t,s)
        // {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        // n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        // if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        // n.queue=[];t=b.createElement(e);t.async=!0;
        // t.src=v;s=b.getElementsByTagName(e)[0];
        // s.parentNode.insertBefore(t,s)}(window, document,'script',
        // 'https://connect.facebook.net/en_US/fbevents.js');
        // fbq('init', 'YOUR_FB_PIXEL_ID');
        // fbq('track', 'PageView');
      }
    }

    // On page load, check consent and load appropriate cookies
    loadCookiesBasedOnConsent();

    /**
     * Make the page content inert or not inert and handle scroll locking
     * @param {boolean} isInert - Whether to make the page inert or not
     */
    function makePageInert(isInert) {
      // Get all the main content areas of the page
      const mainContent = document.getElementById('main-content');
      const header = document.querySelector('header');
      const siteHeader = document.querySelector('.bg-white.w-full.h-\\[170px\\]');
      const footer = document.querySelector('footer');

      // Elements to exclude from being inert (the cookie banner itself)
      const elementsToExclude = [cookieConsent, cookieModal];

      // Elements to make inert
      const elementsToMakeInert = [mainContent, header, siteHeader, footer].filter(el => el !== null);

      if (isInert) {
        // Store current scroll position
        scrollPosition = window.scrollY;

        // Make the page inert
        elementsToMakeInert.forEach(element => {
          if (!elementsToExclude.includes(element)) {
            element.setAttribute('inert', '');
          }
        });

        // Prevent scrolling using an accessible approach
        // 1. Add a style attribute to the body to prevent scrolling
        document.body.style.position = 'fixed';
        document.body.style.top = `-${scrollPosition}px`;
        document.body.style.width = '100%';
        document.body.style.overflowY = 'scroll'; // Maintain scrollbar to prevent layout shift
      } else {
        // Remove inert attribute
        elementsToMakeInert.forEach(element => {
          if (element.hasAttribute('inert')) {
            element.removeAttribute('inert');
          }
        });

        // Restore scrolling
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        document.body.style.overflowY = '';

        // Restore scroll position
        window.scrollTo(0, scrollPosition);
      }
    }
  });