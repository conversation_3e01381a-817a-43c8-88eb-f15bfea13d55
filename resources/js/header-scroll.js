/**
 * Header scroll behavior
 * Reduces header height on scroll for md+ screens
 */

document.addEventListener('DOMContentLoaded', function() {
    const headerLogoSection = document.querySelector('.header-logo-section');
    
    if (!headerLogoSection) {
        return;
    }

    let isScrolled = false;
    let ticking = false;

    function updateHeaderOnScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const shouldBeScrolled = scrollTop > 50; // Trigger after 50px of scroll

        if (shouldBeScrolled !== isScrolled) {
            isScrolled = shouldBeScrolled;
            
            if (isScrolled) {
                headerLogoSection.classList.add('scrolled');
            } else {
                headerLogoSection.classList.remove('scrolled');
            }
        }

        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateHeaderOnScroll);
            ticking = true;
        }
    }

    // Listen for scroll events
    window.addEventListener('scroll', requestTick, { passive: true });
    
    // Check initial state
    updateHeaderOnScroll();
});
