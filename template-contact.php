<?php
/**
 * Template Name: Contact Us
 *
 * The template for displaying the contact us page with office locations and map.
 *
 * @package Marketing_Lawyers
 */

get_header();

// Get all published office posts
$offices_query = new WP_Query([
    'post_type' => 'office',
    'post_status' => 'publish',
    'posts_per_page' => -1,
    'orderby' => 'title',
    'order' => 'ASC'
]);

// Prepare office data array
$office_locations = [];

if ($offices_query->have_posts()) {
    while ($offices_query->have_posts()) {
        $offices_query->the_post();

        // Get office details from ACF fields
        $office_name = get_field('office_name') ? get_field('office_name') : get_the_title();
        $office_location = get_field('office_location');
        $dx_address = get_field('dx_address');

        // Get office hours and ensure it's a string
        $office_hours = get_field('office_hours');


        // Only add offices that have location data
        if (!empty($office_location) && !empty($office_location['address'])) {
            $office_locations[] = [
                'id' => get_the_ID(),
                'office_name' => $office_name,
                'office_address' => $office_location['address'],
                'office_lat' => $office_location['lat'],
                'office_lng' => $office_location['lng'],
                'office_phone' => get_field('office_phone'),
                'office_email' => get_field('office_email'),
                'office_hours' => $office_hours,
                'permalink' => get_permalink()
            ];
        }
    }

    // Reset post data
    wp_reset_postdata();
}

// Get Google Maps API key from options
$google_maps_api_key = function_exists('get_field') ? get_field('google_maps_api_key', 'option') : '';

// Get the contact form shortcode from ACF if available
$contact_form_shortcode = function_exists('get_field') ? get_field('contact_form_shortcode') : '';
if (empty($contact_form_shortcode)) {
    // Fallback to a field in the options page
    $contact_form_shortcode = function_exists('get_field') ? get_field('default_contact_form_shortcode', 'option') : '';
}

?>

<div class="container mx-auto px-4 py-12">
    <!-- Google Map Section - Now at the top -->
    <?php if (!empty($google_maps_api_key) && !empty($office_locations)) : ?>
        <div class="mb-12">
            <h2 class="text-2xl font-bold mb-6">Find Us</h2>
            <div id="office-map" class="h-[500px] w-full rounded shadow-md"></div>
        </div>
    <?php endif; ?>

    <!-- Two-column layout with offices on left, form on right -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Office Locations Section - Now on the left -->
        <div>
            <h2 class="text-2xl font-bold mb-6">Our Offices</h2>

            <?php if (!empty($office_locations) && is_array($office_locations)) : ?>
                <div class="space-y-8">
                    <?php foreach ($office_locations as $index => $office) :
                        // Get office details
                        $name = isset($office['office_name']) ? $office['office_name'] : '';
                        $address = isset($office['office_address']) ? $office['office_address'] : '';
                        $phone = isset($office['office_phone']) ? $office['office_phone'] : '';
                        $email = isset($office['office_email']) ? $office['office_email'] : '';

                        $permalink = isset($office['permalink']) ? $office['permalink'] : '';
                    ?>
                        <div id="office-<?php echo esc_attr($index); ?>" class="bg-white rounded shadow-md p-6">
                            <h3 class="text-xl font-bold mb-2">
                                <?php if (!empty($permalink)) : ?>
                                    <a href="<?php echo esc_url($permalink); ?>" class="hover:text-primary">
                                        <?php echo esc_html($name); ?>
                                    </a>
                                <?php else : ?>
                                    <?php echo esc_html($name); ?>
                                <?php endif; ?>
                            </h3>

                            <div class="flex flex-col">
                                <div class="mb-4 md:mb-0">
                                    <div class="flex items-start mb-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                        <div>
                                            <?php
                                            // Extract postal code from address
                                            $postal_code = '';

                                            // UK postal code regex pattern
                                            $uk_postcode_pattern = '/([A-Z]{1,2}[0-9][A-Z0-9]? ?[0-9][A-Z]{2}|[A-Z]{1,2}[0-9][A-Z0-9]?)/i';

                                            // Check if we can find a UK postal code
                                            if (preg_match($uk_postcode_pattern, $address, $matches)) {
                                                $postal_code = $matches[0];
                                                // Remove the postal code from the address for display
                                                $address_without_postcode = preg_replace($uk_postcode_pattern, '', $address);
                                                // Clean up any trailing commas or whitespace
                                                $address_without_postcode = rtrim($address_without_postcode, ", \t\n\r\0\x0B");
                                                echo nl2br(esc_html($address_without_postcode));
                                            } else {
                                                echo nl2br(esc_html($address));
                                            }
                                            ?>
                                            <?php if (!empty($postal_code)) : ?>
                                                <div class="font-bold mt-1"><?php echo esc_html($postal_code); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <?php if (!empty($phone)) : ?>
                                        <div class="flex items-center mb-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                            </svg>
                                            <a href="tel:<?php echo esc_attr(preg_replace('/[^0-9+]/', '', $phone)); ?>" class="hover:text-primary">
                                                <?php echo esc_html($phone); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($email)) : ?>
                                        <div class="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                            <a href="mailto:<?php echo esc_attr($email); ?>" class="hover:text-primary">
                                                <?php echo esc_html($email); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <?php if ($office_hours && count($office_hours) > 0) { ?>
                            <h3 class="text-xl font-bold mb-4 text-gray-800 uppercase">Office Hours</h3>
                            <div class="bg-gray-50 rounded p-4">
                                <table class="w-full">
                                    <tbody>
                                        <?php foreach ($office_hours as $hours) { ?>
                                            <tr class="border-b border-gray-200 last:border-0">
                                                <td class="py-3 font-medium capitalize"><?php echo esc_html($hours['opening_day']); ?></td>
                                                <td class="py-3 text-right">
                                                    <?php if ($hours['office_closed']) { ?>
                                                        <span class="text-red-600">Closed</span>
                                                    <?php } else { ?>
                                                        <?php
                                        $opening_hours = isset($hours['opening_hours']) ? $hours['opening_hours'] : '';
                                                        $closing_hours = isset($hours['closing_hours']) ? $hours['closing_hours'] : '';

                                                        if ($opening_hours && $closing_hours) {
                                                            echo esc_html($opening_hours . ' - ' . $closing_hours);
                                                        } else {
                                                            echo 'By appointment';
                                                        }
                                                        ?>
                                                    <?php } ?>
                                                </td>
                                            </tr>
                                        <?php } ?>
                                    </tbody>
                                </table>
                            </div>
                    <?php } ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else : ?>
                <p class="text-gray-600">No office locations have been added yet.</p>
            <?php endif; ?>
        </div>

        <!-- Contact Form Section - Now on the right -->
        <div class="bg-white rounded shadow-md p-8">
            <?php echo do_shortcode('[contact-form-7 id="110" title="Contact form"]'); ?>
        </div>
    </div>

</div>

<?php if (!empty($google_maps_api_key) && !empty($office_locations)) : ?>
<script>
    // Make initMap function globally accessible
    window.initMap = function() {
        console.log('initMap function called');

        try {
            // Check if the map container exists
            const mapElement = document.getElementById("office-map");
            if (!mapElement) {
                console.error('Map container not found');
                return;
            }

            const map = new google.maps.Map(document.getElementById("office-map"), {
            zoom: 5, // Country-level view
            center: {lat: 54.5, lng: -4.0}, // Center on UK by default
            styles: [
                {
                    "featureType": "administrative",
                    "elementType": "all",
                    "stylers": [{"saturation": "-100"}]
                },
                {
                    "featureType": "administrative.province",
                    "elementType": "all",
                    "stylers": [{"visibility": "off"}]
                },
                {
                    "featureType": "landscape",
                    "elementType": "all",
                    "stylers": [{"saturation": -100}, {"lightness": 65}, {"visibility": "on"}]
                },
                {
                    "featureType": "poi",
                    "elementType": "all",
                    "stylers": [{"saturation": -100}, {"lightness": "50"}, {"visibility": "simplified"}]
                },
                {
                    "featureType": "road",
                    "elementType": "all",
                    "stylers": [{"saturation": "-100"}]
                },
                {
                    "featureType": "road.highway",
                    "elementType": "all",
                    "stylers": [{"visibility": "simplified"}]
                },
                {
                    "featureType": "road.arterial",
                    "elementType": "all",
                    "stylers": [{"lightness": "30"}]
                },
                {
                    "featureType": "road.local",
                    "elementType": "all",
                    "stylers": [{"lightness": "40"}]
                },
                {
                    "featureType": "transit",
                    "elementType": "all",
                    "stylers": [{"saturation": -100}, {"visibility": "simplified"}]
                },
                {
                    "featureType": "water",
                    "elementType": "geometry",
                    "stylers": [{"hue": "#ffff00"}, {"lightness": -25}, {"saturation": -97}]
                },
                {
                    "featureType": "water",
                    "elementType": "labels",
                    "stylers": [{"lightness": -25}, {"saturation": -100}]
                }
            ]
        });

        const bounds = new google.maps.LatLngBounds();
        const infoWindow = new google.maps.InfoWindow();

        // Create markers for each office
        const offices = <?php
            // Ensure all values are properly sanitized for JSON
            $sanitized_locations = array_map(function($office) {
                // Make sure all values are strings or null
                foreach ($office as $key => $value) {
                    if (is_array($value)) {
                        $office[$key] = implode("\n", array_filter($value));
                    }
                }
                return $office;
            }, $office_locations);

            echo json_encode($sanitized_locations);
        ?>;

        offices.forEach((office, index) => {
            // Skip if no coordinates
            if (!office.office_lat || !office.office_lng) return;

            const position = {
                lat: parseFloat(office.office_lat),
                lng: parseFloat(office.office_lng)
            };

            // Skip if invalid coordinates
            if (isNaN(position.lat) || isNaN(position.lng)) return;

            const marker = new google.maps.Marker({
                position: position,
                map: map,
                title: office.office_name,
                animation: google.maps.Animation.DROP,
                label: {
                    text: (index + 1).toString(),
                    color: 'white',
                    fontWeight: 'bold'
                },
                icon: {
                    path: google.maps.SymbolPath.CIRCLE,
                    fillColor: '#3B82F6', // Tailwind primary blue
                    fillOpacity: 1,
                    strokeColor: '#FFFFFF',
                    strokeWeight: 2,
                    scale: 12
                }
            });

            bounds.extend(position);

            // Extract postal code from address
            let address = office.office_address;
            let postalCode = '';

            // UK postal code regex pattern
            const ukPostcodePattern = /([A-Z]{1,2}[0-9][A-Z0-9]? ?[0-9][A-Z]{2}|[A-Z]{1,2}[0-9][A-Z0-9]?)/i;

            // Check if we can find a UK postal code
            const postcodeMatch = address.match(ukPostcodePattern);
            if (postcodeMatch) {
                postalCode = postcodeMatch[0];
                // Remove the postal code from the address for display
                address = address.replace(ukPostcodePattern, '');
                // Clean up any trailing commas or whitespace
                address = address.replace(/,\s*$/, '');
            }

            // Create info window content
            const contentString = `
                <div class="p-3">
                    <h3 class="font-bold text-lg mb-2">
                        ${office.permalink ? `<a href="${office.permalink}" class="text-primary hover:underline">${office.office_name}</a>` : office.office_name}
                    </h3>
                    <p class="mb-1">${address.replace(/\n/g, '<br>')}</p>
                    ${postalCode ? `<p class="mb-2 font-bold">${postalCode}</p>` : ''}
                    ${office.office_phone ? `<p class="mb-1"><strong>Phone:</strong> ${office.office_phone}</p>` : ''}
                    ${office.office_email ? `<p class="mb-1"><strong>Email:</strong> <a href="mailto:${office.office_email}" class="text-primary hover:underline">${office.office_email}</a></p>` : ''}
                    ${office.permalink ? `<p class="mt-3"><a href="${office.permalink}" class="text-primary hover:underline font-medium">View Office Details →</a></p>` : ''}
                </div>
            `;

            marker.addListener("click", () => {
                infoWindow.setContent(contentString);
                infoWindow.open(map, marker);
            });
        });

        // First, center the map on the markers if there are any
        if (!bounds.isEmpty()) {
            map.setCenter(bounds.getCenter());
        }

        // Force a specific zoom level
        map.setZoom(10);

        // Add a safety measure to ensure the zoom level is applied
        setTimeout(function() {
            map.setZoom(10);
            console.log('Map zoom level forced to 5');
        }, 500);

        } catch (error) {
            console.error('Error initializing Google Map:', error);
            document.getElementById('office-map').innerHTML = '<div class="p-4 bg-red-50 text-red-700">Error loading map. Please try again later.</div>';
        }
    }
</script>
<!-- Add a script to check if the map loaded correctly -->
<script>
    // Check if the map initialized correctly after a timeout
    setTimeout(function() {
        if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
            console.error('Google Maps failed to load');
            document.getElementById('office-map').innerHTML = '<div class="p-4 bg-red-50 text-red-700">Error loading map. Please try again later.</div>';
        }
    }, 3000);
</script>
<script src="https://maps.googleapis.com/maps/api/js?key=<?php echo esc_attr($google_maps_api_key); ?>&callback=initMap" async defer></script>
<?php endif; ?>

<?php get_footer(); ?>
